<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل المنتج - Easy Store</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/custom.css">
    
    <style>
        .product-gallery {
            position: relative;
        }
        
        .main-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .thumbnail-images {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            overflow-x: auto;
            padding: 5px 0;
        }
        
        .thumbnail {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .thumbnail:hover,
        .thumbnail.active {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }
        
        .product-info {
            padding: 2rem;
        }
        
        .product-title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .product-price {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }
        
        .product-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #666;
            margin-bottom: 2rem;
        }
        
        .product-details {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #495057;
        }
        
        .detail-value {
            color: #333;
            font-weight: 500;
        }
        
        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .quantity-input {
            width: 80px;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 0.5rem;
        }
        
        .btn-quantity {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-quantity:hover {
            background: var(--primary-dark);
            transform: scale(1.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .btn-add-cart {
            flex: 2;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-add-cart:hover {
            background: linear-gradient(135deg, #38a169, #2f855a);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
        }
        
        .btn-back {
            flex: 1;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-back:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .video-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .stock-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .stock-available {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            border: 2px solid rgba(46, 204, 113, 0.3);
        }
        
        .stock-low {
            background: rgba(255, 193, 7, 0.1);
            color: #f39c12;
            border: 2px solid rgba(255, 193, 7, 0.3);
        }
        
        .stock-out {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 2px solid rgba(231, 76, 60, 0.3);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header sticky-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-light me-3" id="cartBtn">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-counter">0</span>
                        </button>
                        <button class="btn btn-outline-light" data-bs-toggle="modal" data-bs-target="#loginModal">
                            <i class="fas fa-user"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="search-container">
                        <input type="text" class="form-control search-input" placeholder="ابحث عن المنتجات...">
                        <button class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-3 text-end">
                    <a href="index.html" class="store-name">
                        <span class="store-icon">ES</span>
                        Easy Store
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container my-5">
        <div class="row">
            <!-- Product Gallery -->
            <div class="col-lg-6">
                <div class="product-gallery">
                    <img id="mainImage" src="" alt="" class="main-image">
                    <div class="thumbnail-images" id="thumbnailImages">
                        <!-- Thumbnails will be loaded here -->
                    </div>
                </div>
                
                <!-- Product Video -->
                <div class="video-container mt-4" id="videoContainer" style="display: none;">
                    <iframe id="productVideo" src="" allowfullscreen></iframe>
                </div>
            </div>
            
            <!-- Product Information -->
            <div class="col-lg-6">
                <div class="product-info">
                    <h1 class="product-title" id="productTitle">اسم المنتج</h1>
                    <div class="product-price" id="productPrice">$0.00</div>
                    <p class="product-description" id="productDescription">وصف المنتج</p>
                    
                    <!-- Product Details -->
                    <div class="product-details">
                        <div class="detail-item">
                            <span class="detail-label">الكمية المتاحة:</span>
                            <span class="detail-value" id="productStock">0</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">البلد المتاح:</span>
                            <span class="detail-value" id="productCountry">غير محدد</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">حالة التوفر:</span>
                            <span class="detail-value">
                                <span class="stock-badge" id="stockBadge">متوفر</span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- Quantity Selector -->
                    <div class="quantity-selector">
                        <span class="detail-label">الكمية:</span>
                        <button class="btn-quantity" onclick="decreaseQuantity()">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" id="quantityInput" value="1" min="1">
                        <button class="btn-quantity" onclick="increaseQuantity()">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button class="btn-add-cart" onclick="addProductToCart()">
                            <i class="fas fa-cart-plus me-2"></i>
                            إضافة للسلة
                        </button>
                        <a href="#" class="btn-back" id="backButton">
                            <i class="fas fa-arrow-right me-2"></i>
                            العودة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Floating Social Media Contact Icons -->
    <div class="floating-social-container">
        <a href="https://www.facebook.com" target="_blank" class="floating-social-btn facebook" title="تواصل معنا على فيسبوك">
            <i class="fab fa-facebook-f"></i>
            <span class="social-tooltip">فيسبوك</span>
        </a>
        <a href="https://t.me/yourusername" target="_blank" class="floating-social-btn telegram" title="تواصل معنا على تيليجرام">
            <i class="fab fa-telegram-plane"></i>
            <span class="social-tooltip">تيليجرام</span>
        </a>
        <a href="https://wa.me/1234567890" target="_blank" class="floating-social-btn whatsapp" title="تواصل معنا على واتساب">
            <i class="fab fa-whatsapp"></i>
            <span class="social-tooltip">واتساب</span>
        </a>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/cart.js"></script>
    <script src="assets/js/software-products.js"></script>
    <script src="assets/js/measurement-products.js"></script>
    <script src="assets/js/welding-products.js"></script>
    <script src="assets/js/product-details.js"></script>
</body>
</html>
