import { db } from '../server/db';
import { categories, products } from '../shared/schema';

async function seed() {
  try {
    console.log('Starting database seeding...');

    // Insert categories
    const categoriesData = [
      {
        name: 'Electronics',
        nameAr: 'الإلكترونيات',
        description: 'Latest gadgets and electronic devices',
        descriptionAr: 'أحدث الأجهزة والتقنيات الإلكترونية',
        icon: 'laptop'
      },
      {
        name: 'Clothing',
        nameAr: 'الملابس',
        description: 'Fashion and clothing for everyone',
        descriptionAr: 'أزياء وملابس للجميع',
        icon: 'tshirt'
      },
      {
        name: 'Books',
        nameAr: 'الكتب',
        description: 'Educational and entertainment books',
        descriptionAr: 'كتب تعليمية وترفيهية',
        icon: 'book'
      },
      {
        name: 'Home & Garden',
        nameAr: 'المنزل والحديقة',
        description: 'Home improvement and garden items',
        descriptionAr: 'تحسين المنزل وأدوات الحديقة',
        icon: 'home'
      }
    ];

    const insertedCategories = await db
      .insert(categories)
      .values(categoriesData)
      .returning();

    console.log('Categories inserted:', insertedCategories.length);

    // Map category names to IDs
    const categoryMap: { [key: string]: number } = {};
    insertedCategories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    // Insert products
    const productsData = [
      {
        name: 'Wireless Bluetooth Headphones',
        nameAr: 'سماعات بلوتوث لاسلكية',
        description: 'Premium quality wireless headphones with noise cancellation and 30-hour battery life. Perfect for music lovers and professionals.',
        descriptionAr: 'سماعات لاسلكية عالية الجودة مع تقنية إلغاء الضوضاء وبطارية تدوم 30 ساعة. مثالية لعشاق الموسيقى والمحترفين.',
        price: '129.99',
        originalPrice: '179.99',
        categoryId: categoryMap['Electronics'],
        sku: 'ELEC-001',
        stock: 25,
        rating: '4.5',
        reviews: 342,
        isFeatured: true,
        specifications: {
          'Battery Life': '30 hours',
          'Connectivity': 'Bluetooth 5.0',
          'Weight': '250g',
          'Warranty': '2 years'
        },
        tags: ['wireless', 'bluetooth', 'headphones', 'audio', 'music'],
        images: []
      },
      {
        name: 'Smart Fitness Watch',
        nameAr: 'ساعة ذكية للياقة البدنية',
        description: 'Track your fitness goals with this advanced smartwatch featuring heart rate monitoring, GPS, and 50+ workout modes.',
        descriptionAr: 'تتبع أهداف اللياقة البدنية مع هذه الساعة الذكية المتقدمة التي تتميز بمراقبة معدل ضربات القلب ونظام GPS وأكثر من 50 وضع تمرين.',
        price: '199.99',
        originalPrice: '249.99',
        categoryId: categoryMap['Electronics'],
        sku: 'ELEC-002',
        stock: 15,
        rating: '4.3',
        reviews: 156,
        isFeatured: true,
        specifications: {
          'Display': '1.4 inch AMOLED',
          'Battery Life': '7 days',
          'Water Resistance': '50m',
          'Compatibility': 'iOS & Android'
        },
        tags: ['smartwatch', 'fitness', 'health', 'gps', 'sports'],
        images: []
      },
      {
        name: 'Advanced Tablet',
        nameAr: 'جهاز تابلت متطور',
        description: 'High-performance tablet with 11-inch display and digital pen. Perfect for drawing, work, and entertainment.',
        descriptionAr: 'تابلت عالي الأداء مع شاشة 11 بوصة وقلم رقمي. مثالي للرسم والعمل والترفيه.',
        price: '349.99',
        originalPrice: '429.99',
        categoryId: categoryMap['Electronics'],
        sku: 'ELEC-003',
        stock: 18,
        rating: '4.6',
        reviews: 278,
        isFeatured: true,
        specifications: {
          'Screen': '11 inch IPS LCD',
          'Processor': 'Octa-core',
          'Storage': '128GB',
          'Battery': '10 hours'
        },
        tags: ['tablet', 'digital', 'drawing', 'work', 'entertainment'],
        images: []
      },
      {
        name: 'Organic Cotton T-Shirt',
        nameAr: 'تيشيرت قطن عضوي',
        description: 'Comfortable and eco-friendly organic cotton t-shirt. Available in multiple colors and sizes. Perfect for casual wear.',
        descriptionAr: 'تيشيرت قطن عضوي مريح وصديق للبيئة. متوفر بألوان ومقاسات متعددة. مثالي للارتداء اليومي.',
        price: '24.99',
        originalPrice: '34.99',
        categoryId: categoryMap['Clothing'],
        sku: 'CLOTH-001',
        stock: 50,
        rating: '4.2',
        reviews: 89,
        isFeatured: false,
        specifications: {
          'Material': '100% Organic Cotton',
          'Care': 'Machine Washable',
          'Fit': 'Regular',
          'Origin': 'Made in USA'
        },
        tags: ['t-shirt', 'organic', 'cotton', 'casual', 'eco-friendly'],
        images: []
      },
      {
        name: 'Luxury Leather Handbag',
        nameAr: 'حقيبة يد جلدية فاخرة',
        description: 'Elegant handbag made from high-quality genuine leather. Modern design with spacious interior.',
        descriptionAr: 'حقيبة يد أنيقة من الجلد الطبيعي عالي الجودة. تصميم عصري ومساحة واسعة.',
        price: '89.99',
        originalPrice: '119.99',
        categoryId: categoryMap['Clothing'],
        sku: 'CLOTH-002',
        stock: 32,
        rating: '4.4',
        reviews: 156,
        isFeatured: true,
        specifications: {
          'Material': '100% Genuine Leather',
          'Dimensions': '30×25×12 cm',
          'Colors': 'Brown, Black, Beige',
          'Compartments': '3 main compartments'
        },
        tags: ['handbag', 'leather', 'luxury', 'elegant', 'modern'],
        images: []
      },
      {
        name: 'Photography Learning Book',
        nameAr: 'كتاب تعلم التصوير الفوتوغرافي',
        description: 'Comprehensive guide to learning photography basics and techniques for beginners and professionals.',
        descriptionAr: 'دليل شامل لتعلم أساسيات وتقنيات التصوير الفوتوغرافي للمبتدئين والمحترفين.',
        price: '32.99',
        originalPrice: '45.99',
        categoryId: categoryMap['Books'],
        sku: 'BOOK-001',
        stock: 85,
        rating: '4.7',
        reviews: 234,
        isFeatured: false,
        specifications: {
          'Pages': '420 pages',
          'Publisher': 'Knowledge Publishing House',
          'Language': 'Arabic',
          'Edition': 'Second Edition'
        },
        tags: ['photography', 'learning', 'camera', 'arts', 'book'],
        images: []
      },
      {
        name: 'Smart Kitchen Tools Set',
        nameAr: 'مجموعة أدوات المطبخ الذكية',
        description: 'Complete set of kitchen tools made from high-quality stainless steel.',
        descriptionAr: 'مجموعة متكاملة من أدوات المطبخ المصنوعة من الستانلس ستيل عالي الجودة.',
        price: '67.99',
        originalPrice: '89.99',
        categoryId: categoryMap['Home & Garden'],
        sku: 'HOME-001',
        stock: 24,
        rating: '4.3',
        reviews: 189,
        isFeatured: true,
        specifications: {
          'Material': 'Stainless Steel 304',
          'Pieces': '12 pieces',
          'Warranty': '5 years',
          'Cleaning': 'Dishwasher safe'
        },
        tags: ['kitchen', 'tools', 'stainless steel', 'cooking', 'high quality'],
        images: []
      },
      {
        name: 'Luxury Prayer Rug',
        nameAr: 'سجادة صلاة فاخرة',
        description: 'Luxury prayer rug with beautiful Islamic design and soft, comfortable texture.',
        descriptionAr: 'سجادة صلاة فاخرة مع تصميم إسلامي جميل وخامة ناعمة ومريحة.',
        price: '45.99',
        originalPrice: '65.99',
        categoryId: categoryMap['Home & Garden'],
        sku: 'HOME-002',
        stock: 45,
        rating: '4.8',
        reviews: 312,
        isFeatured: true,
        specifications: {
          'Material': 'Cotton and Silk',
          'Dimensions': '120×70 cm',
          'Design': 'Islamic patterns',
          'Weight': '800g'
        },
        tags: ['prayer rug', 'prayer', 'islamic', 'luxury', 'cotton'],
        images: []
      }
    ];

    const insertedProducts = await db
      .insert(products)
      .values(productsData)
      .returning();

    console.log('Products inserted:', insertedProducts.length);
    console.log('Database seeding completed successfully!');
    
  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}

// Run the seed function if this file is executed directly
if (require.main === module) {
  seed()
    .then(() => {
      console.log('Seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

export { seed };