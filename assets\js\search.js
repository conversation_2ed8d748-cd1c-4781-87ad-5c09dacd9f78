/**
 * Search Functionality JavaScript
 * Handles product search, filtering, and related UI interactions
 */

// Search configuration
const searchConfig = {
    minSearchLength: 2,
    maxSuggestions: 8,
    debounceDelay: 300,
    highlightClass: 'search-highlight'
};

// Search state
let searchState = {
    currentQuery: '',
    suggestions: [],
    isSearching: false,
    searchHistory: JSON.parse(localStorage.getItem('searchHistory')) || []
};

// Initialize search functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeSearch();
});

/**
 * Initialize search functionality
 */
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    
    if (searchInput) {
        // Add event listeners
        searchInput.addEventListener('input', debounce(handleSearchInput, searchConfig.debounceDelay));
        searchInput.addEventListener('keydown', handleSearchKeydown);
        searchInput.addEventListener('focus', handleSearchFocus);
        searchInput.addEventListener('blur', handleSearchBlur);
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearchSubmit);
    }
    
    // Handle clicks outside search
    document.addEventListener('click', handleDocumentClick);
    
    // Initialize from URL parameters
    initializeFromURL();
    
    // Load search history
    loadSearchHistory();
}

/**
 * Handle search input changes
 */
function handleSearchInput(event) {
    const query = event.target.value.trim();
    searchState.currentQuery = query;
    
    if (query.length >= searchConfig.minSearchLength) {
        showSearchSuggestions(query);
    } else {
        hideSearchSuggestions();
    }
}

/**
 * Handle search input keydown events
 */
function handleSearchKeydown(event) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    
    switch (event.key) {
        case 'Enter':
            event.preventDefault();
            handleSearchSubmit();
            break;
        case 'Escape':
            hideSearchSuggestions();
            event.target.blur();
            break;
        case 'ArrowDown':
            if (suggestionsContainer && suggestionsContainer.style.display !== 'none') {
                event.preventDefault();
                navigateSuggestions('down');
            }
            break;
        case 'ArrowUp':
            if (suggestionsContainer && suggestionsContainer.style.display !== 'none') {
                event.preventDefault();
                navigateSuggestions('up');
            }
            break;
    }
}

/**
 * Handle search input focus
 */
function handleSearchFocus(event) {
    const query = event.target.value.trim();
    if (query.length >= searchConfig.minSearchLength) {
        showSearchSuggestions(query);
    } else if (searchState.searchHistory.length > 0) {
        showSearchHistory();
    }
}

/**
 * Handle search input blur
 */
function handleSearchBlur(event) {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
        hideSearchSuggestions();
    }, 200);
}

/**
 * Handle search form submission
 */
function handleSearchSubmit() {
    const searchInput = document.getElementById('searchInput');
    const query = searchInput ? searchInput.value.trim() : '';
    
    if (!query) {
        showToast('Please enter a search term', 'warning');
        return;
    }
    
    performSearch(query);
}

/**
 * Handle document clicks (for closing suggestions)
 */
function handleDocumentClick(event) {
    const searchContainer = event.target.closest('.search-container');
    if (!searchContainer) {
        hideSearchSuggestions();
    }
}

/**
 * Perform search operation
 */
function performSearch(query) {
    if (!query) {
        return;
    }
    
    searchState.isSearching = true;
    updateSearchUI(true);
    
    // Add to search history
    addToSearchHistory(query);
    
    // Hide suggestions
    hideSearchSuggestions();
    
    // In a Blogger environment, redirect to search results
    if (typeof window !== 'undefined') {
        const searchUrl = `/search?q=${encodeURIComponent(query)}`;
        
        // Update URL without page reload for SPA behavior
        if (window.history && window.history.pushState) {
            window.history.pushState({search: query}, `Search: ${query}`, searchUrl);
        }
        
        // Trigger search results display
        displaySearchResults(query);
    }
    
    // Track search for analytics
    trackSearch(query);
}

/**
 * Display search results
 */
function displaySearchResults(query) {
    // Show loading state
    showSearchLoading();
    
    // Simulate search delay (in real implementation, this would be an API call)
    setTimeout(() => {
        const results = searchProducts(query);
        renderSearchResults(query, results);
        updateSearchUI(false);
        searchState.isSearching = false;
    }, 500);
}

/**
 * Search products (mock implementation)
 */
function searchProducts(query) {
    // In a real Blogger implementation, this would search through blog posts
    // For now, return empty results as products are handled by Blogger template
    const searchTerms = query.toLowerCase().split(' ').filter(term => term.length > 0);
    
    // Mock search logic - in real implementation, this would query Blogger's API
    return {
        query: query,
        results: [],
        totalResults: 0,
        searchTime: Date.now()
    };
}

/**
 * Render search results
 */
function renderSearchResults(query, searchData) {
    const resultsContainer = document.getElementById('searchResults') || createSearchResultsContainer();
    
    if (searchData.totalResults === 0) {
        resultsContainer.innerHTML = `
            <div class="search-no-results text-center py-5">
                <i class="fas fa-search display-4 text-muted mb-3"></i>
                <h4>No results found for "${escapeHtml(query)}"</h4>
                <p class="text-muted">Try different keywords or browse our categories.</p>
                <div class="mt-4">
                    <button class="btn btn-primary me-2" onclick="clearSearch()">
                        <i class="fas fa-times me-1"></i>Clear Search
                    </button>
                    <button class="btn btn-outline-primary" onclick="showCategories()">
                        <i class="fas fa-th-large me-1"></i>Browse Categories
                    </button>
                </div>
            </div>
        `;
    } else {
        // In a real implementation, render actual search results
        resultsContainer.innerHTML = generateSearchResultsHTML(searchData);
    }
    
    // Scroll to results
    resultsContainer.scrollIntoView({ behavior: 'smooth' });
}

/**
 * Create search results container
 */
function createSearchResultsContainer() {
    const container = document.createElement('div');
    container.id = 'searchResults';
    container.className = 'search-results container my-5';
    
    // Insert after main content
    const main = document.querySelector('main') || document.body;
    main.appendChild(container);
    
    return container;
}

/**
 * Show search suggestions
 */
function showSearchSuggestions(query) {
    const suggestions = generateSuggestions(query);
    displaySuggestions(suggestions);
}

/**
 * Generate search suggestions
 */
function generateSuggestions(query) {
    const suggestions = [];
    const queryLower = query.toLowerCase();
    
    // Product suggestions (mock data)
    const productSuggestions = [
        { type: 'product', text: 'Electronics', category: 'Electronics', icon: 'laptop' },
        { type: 'product', text: 'Clothing', category: 'Clothing', icon: 'tshirt' },
        { type: 'product', text: 'Books', category: 'Books', icon: 'book' },
        { type: 'product', text: 'Home & Garden', category: 'Home', icon: 'home' }
    ].filter(item => item.text.toLowerCase().includes(queryLower));
    
    suggestions.push(...productSuggestions);
    
    // Search history suggestions
    const historySuggestions = searchState.searchHistory
        .filter(term => term.toLowerCase().includes(queryLower) && term.toLowerCase() !== queryLower)
        .slice(0, 3)
        .map(term => ({ type: 'history', text: term, icon: 'history' }));
    
    suggestions.push(...historySuggestions);
    
    return suggestions.slice(0, searchConfig.maxSuggestions);
}

/**
 * Display search suggestions
 */
function displaySuggestions(suggestions) {
    let suggestionsContainer = document.getElementById('searchSuggestions');
    
    if (!suggestionsContainer) {
        suggestionsContainer = createSuggestionsContainer();
    }
    
    if (suggestions.length === 0) {
        hideSearchSuggestions();
        return;
    }
    
    const suggestionsHTML = suggestions.map((suggestion, index) => `
        <div class="suggestion-item d-flex align-items-center p-2 border-bottom" 
             data-suggestion="${escapeHtml(suggestion.text)}" 
             data-index="${index}"
             onclick="selectSuggestion('${escapeHtml(suggestion.text)}')">
            <i class="fas fa-${suggestion.icon} text-muted me-3"></i>
            <div class="flex-grow-1">
                <div class="suggestion-text">${highlightQuery(suggestion.text, searchState.currentQuery)}</div>
                ${suggestion.category ? `<small class="text-muted">${suggestion.category}</small>` : ''}
                ${suggestion.type === 'history' ? `<small class="text-muted">Recent search</small>` : ''}
            </div>
            ${suggestion.type === 'history' ? 
                `<button class="btn btn-sm btn-outline-danger" onclick="removeFromHistory('${escapeHtml(suggestion.text)}', event)">
                    <i class="fas fa-times"></i>
                </button>` : 
                ''
            }
        </div>
    `).join('');
    
    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

/**
 * Create suggestions container
 */
function createSuggestionsContainer() {
    const container = document.createElement('div');
    container.id = 'searchSuggestions';
    container.className = 'search-suggestions position-absolute bg-white border rounded shadow-lg';
    container.style.cssText = `
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1050;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    `;
    
    const searchContainer = document.querySelector('.search-container');
    if (searchContainer) {
        searchContainer.style.position = 'relative';
        searchContainer.appendChild(container);
    }
    
    return container;
}

/**
 * Hide search suggestions
 */
function hideSearchSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

/**
 * Show search history
 */
function showSearchHistory() {
    if (searchState.searchHistory.length === 0) {
        return;
    }
    
    const historySuggestions = searchState.searchHistory
        .slice(0, 5)
        .map(term => ({ type: 'history', text: term, icon: 'history' }));
    
    displaySuggestions(historySuggestions);
}

/**
 * Navigate suggestions with keyboard
 */
function navigateSuggestions(direction) {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (!suggestionsContainer) return;
    
    const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');
    if (suggestions.length === 0) return;
    
    const currentActive = suggestionsContainer.querySelector('.suggestion-item.active');
    let nextIndex = 0;
    
    if (currentActive) {
        const currentIndex = parseInt(currentActive.dataset.index);
        if (direction === 'down') {
            nextIndex = currentIndex + 1 >= suggestions.length ? 0 : currentIndex + 1;
        } else {
            nextIndex = currentIndex - 1 < 0 ? suggestions.length - 1 : currentIndex - 1;
        }
        currentActive.classList.remove('active');
    }
    
    suggestions[nextIndex].classList.add('active');
    suggestions[nextIndex].scrollIntoView({ block: 'nearest' });
}

/**
 * Select a suggestion
 */
function selectSuggestion(suggestionText) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = suggestionText;
    }
    
    hideSearchSuggestions();
    performSearch(suggestionText);
}

/**
 * Add search term to history
 */
function addToSearchHistory(query) {
    const normalizedQuery = query.trim();
    if (!normalizedQuery || normalizedQuery.length < 2) {
        return;
    }
    
    // Remove if already exists
    searchState.searchHistory = searchState.searchHistory.filter(term => 
        term.toLowerCase() !== normalizedQuery.toLowerCase()
    );
    
    // Add to beginning
    searchState.searchHistory.unshift(normalizedQuery);
    
    // Limit history size
    searchState.searchHistory = searchState.searchHistory.slice(0, 10);
    
    // Save to localStorage
    saveSearchHistory();
}

/**
 * Remove term from search history
 */
function removeFromHistory(term, event) {
    if (event) {
        event.stopPropagation();
    }
    
    searchState.searchHistory = searchState.searchHistory.filter(historyTerm => 
        historyTerm !== term
    );
    
    saveSearchHistory();
    
    // Refresh suggestions if showing history
    const searchInput = document.getElementById('searchInput');
    if (searchInput && searchInput.value.trim().length < searchConfig.minSearchLength) {
        showSearchHistory();
    }
}

/**
 * Clear search
 */
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        searchInput.focus();
    }
    
    hideSearchSuggestions();
    
    // Clear search results
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // Reset URL
    if (window.history && window.history.pushState) {
        window.history.pushState({}, document.title, window.location.pathname);
    }
}

/**
 * Show categories
 */
function showCategories() {
    const categoriesSection = document.querySelector('.category-card').closest('section');
    if (categoriesSection) {
        categoriesSection.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * Update search UI
 */
function updateSearchUI(isSearching) {
    const searchBtn = document.getElementById('searchBtn');
    const searchInput = document.getElementById('searchInput');
    
    if (searchBtn) {
        if (isSearching) {
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            searchBtn.disabled = true;
        } else {
            searchBtn.innerHTML = '<i class="fas fa-search"></i>';
            searchBtn.disabled = false;
        }
    }
    
    if (searchInput) {
        searchInput.disabled = isSearching;
    }
}

/**
 * Show search loading state
 */
function showSearchLoading() {
    const resultsContainer = document.getElementById('searchResults') || createSearchResultsContainer();
    
    resultsContainer.innerHTML = `
        <div class="search-loading text-center py-5">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Searching...</span>
            </div>
            <h5>Searching...</h5>
            <p class="text-muted">Please wait while we find the best results for you.</p>
        </div>
    `;
}

/**
 * Highlight query terms in text
 */
function highlightQuery(text, query) {
    if (!query) return escapeHtml(text);
    
    const escapedText = escapeHtml(text);
    const escapedQuery = escapeHtml(query);
    const regex = new RegExp(`(${escapedQuery})`, 'gi');
    
    return escapedText.replace(regex, `<mark class="${searchConfig.highlightClass}">$1</mark>`);
}

/**
 * Initialize search from URL parameters
 */
function initializeFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('q');
    
    if (searchQuery) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = searchQuery;
        }
        
        // Perform search after a short delay
        setTimeout(() => {
            displaySearchResults(searchQuery);
        }, 100);
    }
}

/**
 * Save search history to localStorage
 */
function saveSearchHistory() {
    try {
        localStorage.setItem('searchHistory', JSON.stringify(searchState.searchHistory));
    } catch (error) {
        console.error('Error saving search history:', error);
    }
}

/**
 * Load search history from localStorage
 */
function loadSearchHistory() {
    try {
        const savedHistory = localStorage.getItem('searchHistory');
        if (savedHistory) {
            searchState.searchHistory = JSON.parse(savedHistory);
        }
    } catch (error) {
        console.error('Error loading search history:', error);
        searchState.searchHistory = [];
    }
}

/**
 * Track search for analytics
 */
function trackSearch(query) {
    console.log('Search performed:', {
        query: query,
        timestamp: new Date().toISOString(),
        resultsFound: 0 // Would be actual count in real implementation
    });
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add search-specific CSS
const searchStyles = document.createElement('style');
searchStyles.textContent = `
    .search-suggestions {
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 8px 8px;
    }
    
    .suggestion-item {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .suggestion-item:hover,
    .suggestion-item.active {
        background-color: #f8f9fa;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
    
    .search-highlight {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: 600;
    }
    
    .search-loading .spinner-border {
        width: 3rem;
        height: 3rem;
    }
    
    .search-no-results i {
        opacity: 0.5;
    }
    
    @media (max-width: 768px) {
        .search-suggestions {
            font-size: 0.9rem;
        }
        
        .suggestion-item {
            padding: 0.75rem 1rem;
        }
    }
`;
document.head.appendChild(searchStyles);

// Export search functions for global access
window.Search = {
    performSearch,
    clearSearch,
    selectSuggestion,
    addToSearchHistory,
    removeFromHistory
};
