// Product Details Page JavaScript
let currentProduct = null;
let currentQuantity = 1;

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    loadProductDetails();
    setupEventListeners();
});

// Load product details from URL parameters
function loadProductDetails() {
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    const category = urlParams.get('category');
    
    if (!productId || !category) {
        showError('معرف المنتج أو الفئة غير موجود');
        return;
    }
    
    // Find product in the appropriate category
    currentProduct = findProductById(productId, category);
    
    if (!currentProduct) {
        showError('المنتج غير موجود');
        return;
    }
    
    // Display product details
    displayProductDetails(currentProduct);
    
    // Set back button URL
    setBackButtonUrl(category);
}

// Find product by ID and category
function findProductById(productId, category) {
    // This will be populated by the category-specific product data
    const productData = {
        software: typeof softwareProducts !== 'undefined' ? softwareProducts : [],
        measurement: typeof measurementProducts !== 'undefined' ? measurementProducts : [],
        welding: typeof weldingProducts !== 'undefined' ? weldingProducts : [],
        electronics: typeof electronicsProducts !== 'undefined' ? electronicsProducts : [],
        programmers: typeof programmersProducts !== 'undefined' ? programmersProducts : [],
        used: typeof usedProducts !== 'undefined' ? usedProducts : []
    };
    
    const products = productData[category] || [];
    return products.find(product => product.id === productId);
}

// Display product details on the page
function displayProductDetails(product) {
    // Update page title
    document.title = `${product.name} - Easy Store`;
    
    // Update product information
    document.getElementById('productTitle').textContent = product.name;
    document.getElementById('productPrice').textContent = `$${product.price}`;
    document.getElementById('productDescription').textContent = product.description;
    document.getElementById('productStock').textContent = product.stock || 'غير محدد';
    document.getElementById('productCountry').textContent = product.country || 'جميع البلدان';
    
    // Update stock badge
    updateStockBadge(product);
    
    // Update main image
    const mainImage = document.getElementById('mainImage');
    mainImage.src = product.image;
    mainImage.alt = product.name;
    
    // Update thumbnail images
    updateThumbnailImages(product);
    
    // Update product video
    updateProductVideo(product);
    
    // Set max quantity
    const quantityInput = document.getElementById('quantityInput');
    quantityInput.max = product.stock || 999;
}

// Update stock badge based on availability
function updateStockBadge(product) {
    const stockBadge = document.getElementById('stockBadge');
    const stock = product.stock || 0;
    
    if (stock === 0) {
        stockBadge.textContent = 'غير متوفر';
        stockBadge.className = 'stock-badge stock-out';
    } else if (stock <= 5) {
        stockBadge.textContent = 'كمية محدودة';
        stockBadge.className = 'stock-badge stock-low';
    } else {
        stockBadge.textContent = 'متوفر';
        stockBadge.className = 'stock-badge stock-available';
    }
}

// Update thumbnail images
function updateThumbnailImages(product) {
    const thumbnailContainer = document.getElementById('thumbnailImages');
    
    // Create array of images (main image + additional images if available)
    const images = [product.image];
    if (product.images && Array.isArray(product.images)) {
        images.push(...product.images);
    }
    
    // Clear existing thumbnails
    thumbnailContainer.innerHTML = '';
    
    // Create thumbnail for each image
    images.forEach((imageSrc, index) => {
        const thumbnail = document.createElement('img');
        thumbnail.src = imageSrc;
        thumbnail.alt = `${product.name} - صورة ${index + 1}`;
        thumbnail.className = `thumbnail ${index === 0 ? 'active' : ''}`;
        thumbnail.onclick = () => changeMainImage(imageSrc, thumbnail);
        thumbnailContainer.appendChild(thumbnail);
    });
}

// Change main image when thumbnail is clicked
function changeMainImage(imageSrc, thumbnailElement) {
    document.getElementById('mainImage').src = imageSrc;
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail').forEach(thumb => thumb.classList.remove('active'));
    thumbnailElement.classList.add('active');
}

// Update product video
function updateProductVideo(product) {
    const videoContainer = document.getElementById('videoContainer');
    const videoFrame = document.getElementById('productVideo');
    
    if (product.videoUrl) {
        // Convert YouTube URL to embed format if needed
        let embedUrl = product.videoUrl;
        if (embedUrl.includes('youtube.com/watch?v=')) {
            const videoId = embedUrl.split('v=')[1].split('&')[0];
            embedUrl = `https://www.youtube.com/embed/${videoId}`;
        } else if (embedUrl.includes('youtu.be/')) {
            const videoId = embedUrl.split('youtu.be/')[1].split('?')[0];
            embedUrl = `https://www.youtube.com/embed/${videoId}`;
        }
        
        videoFrame.src = embedUrl;
        videoContainer.style.display = 'block';
    } else {
        videoContainer.style.display = 'none';
    }
}

// Set back button URL based on category
function setBackButtonUrl(category) {
    const backButton = document.getElementById('backButton');
    const categoryUrls = {
        software: 'software.html',
        measurement: 'measurement.html',
        welding: 'welding.html',
        electronics: 'electronics.html',
        programmers: 'programmers.html',
        used: 'used.html'
    };
    
    backButton.href = categoryUrls[category] || 'index.html';
}

// Setup event listeners
function setupEventListeners() {
    // Quantity input change
    const quantityInput = document.getElementById('quantityInput');
    quantityInput.addEventListener('change', function() {
        const value = parseInt(this.value);
        if (value < 1) {
            this.value = 1;
            currentQuantity = 1;
        } else if (currentProduct && value > currentProduct.stock) {
            this.value = currentProduct.stock;
            currentQuantity = currentProduct.stock;
        } else {
            currentQuantity = value;
        }
    });
}

// Increase quantity
function increaseQuantity() {
    const quantityInput = document.getElementById('quantityInput');
    const maxStock = currentProduct ? currentProduct.stock : 999;
    
    if (currentQuantity < maxStock) {
        currentQuantity++;
        quantityInput.value = currentQuantity;
    }
}

// Decrease quantity
function decreaseQuantity() {
    const quantityInput = document.getElementById('quantityInput');
    
    if (currentQuantity > 1) {
        currentQuantity--;
        quantityInput.value = currentQuantity;
    }
}

// Add product to cart with selected quantity
function addProductToCart() {
    if (!currentProduct) {
        showToast('خطأ في تحميل بيانات المنتج', 'error');
        return;
    }
    
    if (currentProduct.stock === 0) {
        showToast('المنتج غير متوفر حالياً', 'warning');
        return;
    }
    
    // Add to cart using the cart.js function
    for (let i = 0; i < currentQuantity; i++) {
        addToCart(currentProduct.id, currentProduct.name, currentProduct.price, currentProduct.image, 1);
    }
    
    showToast(`تم إضافة ${currentQuantity} من ${currentProduct.name} إلى السلة`, 'success');
}

// Show error message
function showError(message) {
    document.getElementById('productTitle').textContent = 'خطأ';
    document.getElementById('productDescription').textContent = message;
    document.querySelector('.action-buttons').style.display = 'none';
}

// Load product data based on category (this will be called by category-specific scripts)
function loadCategoryProducts(category, products) {
    window[category + 'Products'] = products;
}
