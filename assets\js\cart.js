/**
 * Shopping Cart JavaScript
 * Handles all cart-related functionality for the e-commerce store
 */

// Cart data structure
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Cart event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
});

/**
 * Initialize cart functionality
 */
function initializeCart() {
    updateCartUI();
    
    // Cart button click handler
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showCart();
        });
    }
    
    // Checkout button handler
    document.addEventListener('click', function(e) {
        if (e.target.id === 'checkoutBtn' || e.target.closest('#checkoutBtn')) {
            handleCheckout();
        }
    });
    
    // Initialize cart from URL parameters if needed
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('cart') === 'show') {
        setTimeout(showCart, 500);
    }
}

/**
 * Add item to cart
 * @param {string} id - Product ID
 * @param {string} name - Product name
 * @param {number} price - Product price
 * @param {string} image - Product image URL (optional)
 * @param {number} quantity - Quantity to add (default: 1)
 */
function addToCart(id, name, price, image = '', quantity = 1) {
    // Validate inputs
    if (!id || !name || !price) {
        showToast('Invalid product information', 'error');
        return false;
    }
    
    // Convert price to number
    const numPrice = parseFloat(price);
    if (isNaN(numPrice) || numPrice < 0) {
        showToast('Invalid price', 'error');
        return false;
    }
    
    // Check if item already exists in cart
    const existingItem = cart.find(item => item.id === id);
    
    if (existingItem) {
        existingItem.quantity += quantity;
        showToast(`Updated ${name} quantity in cart`, 'success');
    } else {
        const newItem = {
            id: id,
            name: name,
            price: numPrice,
            image: image || '',
            quantity: quantity,
            addedAt: new Date().toISOString()
        };
        
        cart.push(newItem);
        showToast(`${name} added to cart`, 'success');
    }
    
    // Save cart to localStorage
    saveCart();
    updateCartUI();
    
    // Animate cart button
    animateCartButton();
    
    return true;
}

/**
 * Remove item from cart
 * @param {string} id - Product ID to remove
 */
function removeFromCart(id) {
    const itemIndex = cart.findIndex(item => item.id === id);
    
    if (itemIndex > -1) {
        const removedItem = cart[itemIndex];
        cart.splice(itemIndex, 1);
        
        saveCart();
        updateCartUI();
        
        showToast(`${removedItem.name} removed from cart`, 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
    }
}

/**
 * Update item quantity in cart
 * @param {string} id - Product ID
 * @param {number} newQuantity - New quantity
 */
function updateQuantity(id, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(id);
        return;
    }
    
    const item = cart.find(item => item.id === id);
    if (item) {
        const oldQuantity = item.quantity;
        item.quantity = parseInt(newQuantity);
        
        saveCart();
        updateCartUI();
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
        
        if (item.quantity !== oldQuantity) {
            showToast(`${item.name} quantity updated`, 'info');
        }
    }
}

/**
 * Clear entire cart
 */
function clearCart() {
    if (cart.length === 0) {
        showToast('Cart is already empty', 'info');
        return;
    }
    
    if (confirm('Are you sure you want to clear your cart?')) {
        cart = [];
        saveCart();
        updateCartUI();
        showToast('Cart cleared', 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
    }
}

/**
 * Get cart total
 * @returns {number} Total cart value
 */
function getCartTotal() {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

/**
 * Get cart item count
 * @returns {number} Total number of items in cart
 */
function getCartItemCount() {
    return cart.reduce((count, item) => count + item.quantity, 0);
}

/**
 * Update cart UI elements
 */
function updateCartUI() {
    // Update cart count badge
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const itemCount = getCartItemCount();
        cartCount.textContent = itemCount;

        // Show/hide cart count based on items
        if (itemCount > 0) {
            cartCount.style.display = 'flex';
            cartCount.classList.add('updated');
            setTimeout(() => {
                cartCount.classList.remove('updated');
            }, 600);
        } else {
            cartCount.style.display = 'none';
        }
    }

    // Update any other cart-related UI elements
    updateCartSummary();
}

/**
 * Update cart summary in UI
 */
function updateCartSummary() {
    const cartSummary = document.getElementById('cartSummary');
    if (cartSummary) {
        const total = getCartTotal();
        const itemCount = getCartItemCount();
        
        cartSummary.innerHTML = `
            <div class="cart-summary">
                <span class="cart-items">${itemCount} item${itemCount !== 1 ? 's' : ''}</span>
                <span class="cart-total">$${total.toFixed(2)}</span>
            </div>
        `;
    }
}

/**
 * Show cart modal
 */
function showCart() {
    const cartModal = document.getElementById('cartModal');
    const cartItems = document.getElementById('cartItems');
    const cartEmpty = document.getElementById('cartEmpty');
    const cartTotal = document.getElementById('cartTotal');
    
    if (!cartModal) {
        console.error('Cart modal not found');
        return;
    }
    
    if (cart.length === 0) {
        // Show empty cart state
        if (cartItems) cartItems.style.display = 'none';
        if (cartEmpty) cartEmpty.style.display = 'block';
        if (cartTotal) cartTotal.textContent = '0.00';
    } else {
        // Show cart items
        if (cartItems) cartItems.style.display = 'block';
        if (cartEmpty) cartEmpty.style.display = 'none';
        
        if (cartItems) {
            cartItems.innerHTML = generateCartItemsHTML();
        }
        
        if (cartTotal) {
            cartTotal.textContent = getCartTotal().toFixed(2);
        }
    }
    
    // Show modal
    const bsModal = new bootstrap.Modal(cartModal);
    bsModal.show();
    
    // Track cart view for analytics
    trackCartView();
}

/**
 * Generate HTML for cart items
 * @returns {string} HTML string for cart items
 */
function generateCartItemsHTML() {
    return cart.map(item => `
        <div class="cart-item d-flex justify-content-between align-items-center py-3 border-bottom" data-item-id="${item.id}">
            <div class="item-info d-flex align-items-center flex-grow-1">
                <div class="item-image me-3">
                    ${item.image ? 
                        `<img src="${item.image}" alt="${item.name}" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">` : 
                        `<div class="placeholder-img d-flex align-items-center justify-content-center bg-light rounded" style="width: 60px; height: 60px;">
                            <i class="fas fa-shopping-bag text-muted"></i>
                        </div>`
                    }
                </div>
                <div class="item-details">
                    <h6 class="mb-1">${escapeHtml(item.name)}</h6>
                    <p class="text-muted mb-0 small">$${item.price.toFixed(2)} each</p>
                    <p class="text-success mb-0 small fw-bold">Subtotal: $${(item.price * item.quantity).toFixed(2)}</p>
                </div>
            </div>
            <div class="quantity-controls d-flex align-items-center">
                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity - 1})" title="Decrease quantity">
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="form-control mx-2 text-center" 
                       value="${item.quantity}" 
                       min="1" 
                       max="99"
                       style="width: 60px;" 
                       onchange="updateQuantity('${item.id}', this.value)"
                       title="Quantity">
                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity + 1})" title="Increase quantity">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart('${item.id}')" title="Remove item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * Handle checkout process
 */
function handleCheckout() {
    if (cart.length === 0) {
        showToast('Your cart is empty. Add some products first!', 'warning');
        return;
    }
    
    // Show loading state
    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        const originalHTML = checkoutBtn.innerHTML;
        checkoutBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        checkoutBtn.disabled = true;
        
        // Simulate checkout process
        setTimeout(() => {
            // In a real implementation, this would integrate with a payment processor
            // For demo purposes, we'll show a success message
            
            const orderTotal = getCartTotal();
            const orderItems = cart.length;
            const orderId = 'ORD-' + Date.now();
            
            showToast(`Order ${orderId} placed successfully! Total: $${orderTotal.toFixed(2)}`, 'success');
            
            // Clear cart after successful checkout
            cart = [];
            saveCart();
            updateCartUI();
            
            // Close cart modal
            const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
            if (cartModal) {
                cartModal.hide();
            }
            
            // Reset checkout button
            checkoutBtn.innerHTML = originalHTML;
            checkoutBtn.disabled = false;
            
            // Track successful checkout
            trackCheckout(orderId, orderTotal, orderItems);
            
        }, 2000);
    }
}

/**
 * Save cart to localStorage
 */
function saveCart() {
    try {
        localStorage.setItem('cart', JSON.stringify(cart));
        localStorage.setItem('cartUpdated', new Date().toISOString());
    } catch (error) {
        console.error('Error saving cart to localStorage:', error);
        showToast('Error saving cart. Please try again.', 'error');
    }
}

/**
 * Load cart from localStorage
 */
function loadCart() {
    try {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            cart = JSON.parse(savedCart);
            
            // Validate cart items
            cart = cart.filter(item => {
                return item.id && item.name && typeof item.price === 'number' && item.quantity > 0;
            });
            
            updateCartUI();
        }
    } catch (error) {
        console.error('Error loading cart from localStorage:', error);
        cart = [];
    }
}

/**
 * Animate cart button when item is added
 */
function animateCartButton() {
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.classList.add('btn-animate');
        setTimeout(() => {
            cartBtn.classList.remove('btn-animate');
        }, 600);
    }

    // Also animate the cart count
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.classList.add('updated');
        setTimeout(() => {
            cartCount.classList.remove('updated');
        }, 600);
    }
}

/**
 * Track cart view for analytics
 */
function trackCartView() {
    // Implement analytics tracking here
    console.log('Cart viewed', {
        itemCount: getCartItemCount(),
        cartTotal: getCartTotal(),
        items: cart.map(item => ({
            id: item.id,
            name: item.name,
            quantity: item.quantity
        }))
    });
}

/**
 * Track checkout for analytics
 */
function trackCheckout(orderId, total, itemCount) {
    // Implement analytics tracking here
    console.log('Checkout completed', {
        orderId: orderId,
        total: total,
        itemCount: itemCount,
        items: cart
    });
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    // Create toast element if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1060';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast
    const toastId = 'toast-' + Date.now();
    const toastElement = document.createElement('div');
    toastElement.id = toastId;
    toastElement.className = `toast align-items-center text-white bg-${getToastBgClass(type)} border-0`;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    
    toastElement.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getToastIcon(type)} me-2"></i>
                ${escapeHtml(message)}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toastElement);
    
    // Show toast
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 5000 : 3000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * Get Bootstrap background class for toast type
 */
function getToastBgClass(type) {
    const classMap = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'primary'
    };
    return classMap[type] || 'primary';
}

/**
 * Get Font Awesome icon for toast type
 */
function getToastIcon(type) {
    const iconMap = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return iconMap[type] || 'info-circle';
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .btn-animate {
        animation: pulse 0.6s ease-in-out;
    }
    
    .badge-animate {
        animation: bounce 0.3s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        80% { transform: translateY(-5px); }
    }
    
    .cart-item:hover {
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }
    
    .quantity-controls input::-webkit-outer-spin-button,
    .quantity-controls input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    
    .quantity-controls input[type=number] {
        -moz-appearance: textfield;
    }
`;
document.head.appendChild(style);

// Initialize cart on page load
loadCart();

// Export cart functions for global access
window.Cart = {
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    showCart,
    getCartTotal,
    getCartItemCount
};
