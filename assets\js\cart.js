/**
 * Shopping Cart JavaScript
 * Handles all cart-related functionality for the e-commerce store
 */

// Cart data structure
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Cart event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeCart();
});

/**
 * Initialize cart functionality
 */
function initializeCart() {
    updateCartUI();
    
    // Cart button click handler
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showCart();
        });
    }
    
    // Checkout button handler
    document.addEventListener('click', function(e) {
        if (e.target.id === 'checkoutBtn' || e.target.closest('#checkoutBtn')) {
            handleCheckout();
        }
    });
    
    // Initialize cart from URL parameters if needed
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('cart') === 'show') {
        setTimeout(showCart, 500);
    }
}

/**
 * Add item to cart
 * @param {string} id - Product ID
 * @param {string} name - Product name
 * @param {number} price - Product price
 * @param {string} image - Product image URL (optional)
 * @param {number} quantity - Quantity to add (default: 1)
 */
function addToCart(id, name, price, image = '', quantity = 1) {
    // Check if user is logged in
    const isLoggedIn = typeof currentUser !== 'undefined' && currentUser !== null;

    if (!isLoggedIn) {
        showToast('يجب تسجيل الدخول لإضافة المنتجات للسلة', 'warning');
        // Show login modal if available
        if (typeof showLoginModal === 'function') {
            showLoginModal();
        }
        return false;
    }

    // Validate inputs
    if (!id || !name || !price) {
        showToast('بيانات المنتج غير مكتملة', 'danger');
        return false;
    }
    
    // Convert price to number
    const numPrice = parseFloat(price);
    if (isNaN(numPrice) || numPrice < 0) {
        showToast('Invalid price', 'error');
        return false;
    }
    
    // Check if item already exists in cart
    const existingItem = cart.find(item => item.id === id);
    
    if (existingItem) {
        existingItem.quantity += quantity;
        showToast(`Updated ${name} quantity in cart`, 'success');
    } else {
        const newItem = {
            id: id,
            name: name,
            price: numPrice,
            image: image || '',
            quantity: quantity,
            addedAt: new Date().toISOString()
        };
        
        cart.push(newItem);
        showToast(`${name} added to cart`, 'success');
    }
    
    // Save cart to localStorage
    saveCart();
    updateCartUI();
    
    // Animate cart button
    animateCartButton();
    
    return true;
}

/**
 * Remove item from cart
 * @param {string} id - Product ID to remove
 */
function removeFromCart(id) {
    const itemIndex = cart.findIndex(item => item.id === id);
    
    if (itemIndex > -1) {
        const removedItem = cart[itemIndex];
        cart.splice(itemIndex, 1);
        
        saveCart();
        updateCartUI();
        
        showToast(`${removedItem.name} removed from cart`, 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
    }
}

/**
 * Update item quantity in cart
 * @param {string} id - Product ID
 * @param {number} newQuantity - New quantity
 */
function updateQuantity(id, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(id);
        return;
    }
    
    const item = cart.find(item => item.id === id);
    if (item) {
        const oldQuantity = item.quantity;
        item.quantity = parseInt(newQuantity);
        
        saveCart();
        updateCartUI();
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
        
        if (item.quantity !== oldQuantity) {
            showToast(`${item.name} quantity updated`, 'info');
        }
    }
}

/**
 * Clear entire cart
 */
function clearCart() {
    if (cart.length === 0) {
        showToast('Cart is already empty', 'info');
        return;
    }
    
    if (confirm('Are you sure you want to clear your cart?')) {
        cart = [];
        saveCart();
        updateCartUI();
        showToast('Cart cleared', 'info');
        
        // Refresh cart modal if open
        const cartModal = document.getElementById('cartModal');
        if (cartModal && cartModal.classList.contains('show')) {
            showCart();
        }
    }
}

/**
 * Get cart total
 * @returns {number} Total cart value
 */
function getCartTotal() {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
}

/**
 * Get cart item count
 * @returns {number} Total number of items in cart
 */
function getCartItemCount() {
    return cart.reduce((count, item) => count + item.quantity, 0);
}

/**
 * Update cart UI elements
 */
function updateCartUI() {
    // Update cart count badge
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        const itemCount = getCartItemCount();
        cartCount.textContent = itemCount;

        // Show/hide cart count based on items
        if (itemCount > 0) {
            cartCount.style.display = 'flex';
            cartCount.classList.add('updated');
            setTimeout(() => {
                cartCount.classList.remove('updated');
            }, 600);
        } else {
            cartCount.style.display = 'none';
        }
    }

    // Update any other cart-related UI elements
    updateCartSummary();
}

/**
 * Update cart summary in UI
 */
function updateCartSummary() {
    const cartSummary = document.getElementById('cartSummary');
    if (cartSummary) {
        const total = getCartTotal();
        const itemCount = getCartItemCount();
        
        cartSummary.innerHTML = `
            <div class="cart-summary">
                <span class="cart-items">${itemCount} item${itemCount !== 1 ? 's' : ''}</span>
                <span class="cart-total">$${total.toFixed(2)}</span>
            </div>
        `;
    }
}

/**
 * Show cart modal
 */
function showCart() {
    const cartModal = document.getElementById('cartModal');
    const cartItems = document.getElementById('cartItems');
    const cartEmpty = document.getElementById('cartEmpty');
    const cartTotal = document.getElementById('cartTotal');
    
    if (!cartModal) {
        console.error('Cart modal not found');
        return;
    }
    
    if (cart.length === 0) {
        // Show empty cart state
        if (cartItems) cartItems.style.display = 'none';
        if (cartEmpty) cartEmpty.style.display = 'block';
        if (cartTotal) cartTotal.textContent = '0.00';
    } else {
        // Show cart items
        if (cartItems) cartItems.style.display = 'block';
        if (cartEmpty) cartEmpty.style.display = 'none';
        
        if (cartItems) {
            cartItems.innerHTML = generateCartItemsHTML();
        }
        
        if (cartTotal) {
            cartTotal.textContent = getCartTotal().toFixed(2);
        }
    }
    
    // Show modal
    const bsModal = new bootstrap.Modal(cartModal);
    bsModal.show();

    // Add event listener for modal close to refresh page
    cartModal.addEventListener('hidden.bs.modal', function() {
        // Refresh products display to ensure proper state
        if (typeof displayProducts === 'function') {
            setTimeout(() => {
                displayProducts();
            }, 100);
        }

        // Re-enable page interactions
        document.body.style.pointerEvents = 'auto';
        document.body.style.overflow = 'auto';

        // Remove any lingering modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });

        // Force page refresh if needed
        if (document.body.classList.contains('modal-open')) {
            document.body.classList.remove('modal-open');
        }
    }, { once: true }); // Use once: true to avoid multiple listeners

    // Track cart view for analytics
    trackCartView();
}

/**
 * Generate HTML for cart items
 * @returns {string} HTML string for cart items
 */
function generateCartItemsHTML() {
    return cart.map(item => `
        <div class="cart-item d-flex justify-content-between align-items-center py-3 border-bottom" data-item-id="${item.id}">
            <div class="item-info d-flex align-items-center flex-grow-1">
                <div class="item-image me-3">
                    ${item.image ? 
                        `<img src="${item.image}" alt="${item.name}" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">` : 
                        `<div class="placeholder-img d-flex align-items-center justify-content-center bg-light rounded" style="width: 60px; height: 60px;">
                            <i class="fas fa-shopping-bag text-muted"></i>
                        </div>`
                    }
                </div>
                <div class="item-details">
                    <h6 class="mb-1">${escapeHtml(item.name)}</h6>
                    <p class="text-muted mb-0 small">$${item.price.toFixed(2)} each</p>
                    <p class="text-success mb-0 small fw-bold">Subtotal: $${(item.price * item.quantity).toFixed(2)}</p>
                </div>
            </div>
            <div class="quantity-controls d-flex align-items-center">
                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity - 1})" title="Decrease quantity">
                    <i class="fas fa-minus"></i>
                </button>
                <input type="number" class="form-control mx-2 text-center" 
                       value="${item.quantity}" 
                       min="1" 
                       max="99"
                       style="width: 60px;" 
                       onchange="updateQuantity('${item.id}', this.value)"
                       title="Quantity">
                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity + 1})" title="Increase quantity">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart('${item.id}')" title="Remove item">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

/**
 * Handle checkout process
 */
function handleCheckout() {
    if (cart.length === 0) {
        showToast('Your cart is empty. Add some products first!', 'warning');
        return;
    }
    
    // Show loading state
    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        const originalHTML = checkoutBtn.innerHTML;
        checkoutBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        checkoutBtn.disabled = true;
        
        // Simulate checkout process
        setTimeout(() => {
            // In a real implementation, this would integrate with a payment processor
            // For demo purposes, we'll show a success message
            
            const orderTotal = getCartTotal();
            const orderItems = cart.length;
            const orderId = 'ORD-' + Date.now();
            
            showToast(`Order ${orderId} placed successfully! Total: $${orderTotal.toFixed(2)}`, 'success');
            
            // Clear cart after successful checkout
            cart = [];
            saveCart();
            updateCartUI();
            
            // Close cart modal
            const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
            if (cartModal) {
                cartModal.hide();
            }
            
            // Reset checkout button
            checkoutBtn.innerHTML = originalHTML;
            checkoutBtn.disabled = false;
            
            // Track successful checkout
            trackCheckout(orderId, orderTotal, orderItems);
            
        }, 2000);
    }
}

/**
 * Save cart to localStorage
 */
function saveCart() {
    try {
        localStorage.setItem('cart', JSON.stringify(cart));
        localStorage.setItem('cartUpdated', new Date().toISOString());
    } catch (error) {
        console.error('Error saving cart to localStorage:', error);
        showToast('Error saving cart. Please try again.', 'error');
    }
}

/**
 * Load cart from localStorage
 */
function loadCart() {
    try {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            cart = JSON.parse(savedCart);
            
            // Validate cart items
            cart = cart.filter(item => {
                return item.id && item.name && typeof item.price === 'number' && item.quantity > 0;
            });
            
            updateCartUI();
        }
    } catch (error) {
        console.error('Error loading cart from localStorage:', error);
        cart = [];
    }
}

/**
 * Animate cart button when item is added
 */
function animateCartButton() {
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.classList.add('btn-animate');
        setTimeout(() => {
            cartBtn.classList.remove('btn-animate');
        }, 600);
    }

    // Also animate the cart count
    const cartCount = document.getElementById('cartCount');
    if (cartCount) {
        cartCount.classList.add('updated');
        setTimeout(() => {
            cartCount.classList.remove('updated');
        }, 600);
    }
}

/**
 * Track cart view for analytics
 */
function trackCartView() {
    // Implement analytics tracking here
    console.log('Cart viewed', {
        itemCount: getCartItemCount(),
        cartTotal: getCartTotal(),
        items: cart.map(item => ({
            id: item.id,
            name: item.name,
            quantity: item.quantity
        }))
    });
}

/**
 * Track checkout for analytics
 */
function trackCheckout(orderId, total, itemCount) {
    // Implement analytics tracking here
    console.log('Checkout completed', {
        orderId: orderId,
        total: total,
        itemCount: itemCount,
        items: cart
    });
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    // Create toast element if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1060';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast
    const toastId = 'toast-' + Date.now();
    const toastElement = document.createElement('div');
    toastElement.id = toastId;
    toastElement.className = `toast align-items-center text-white bg-${getToastBgClass(type)} border-0`;
    toastElement.setAttribute('role', 'alert');
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    
    toastElement.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${getToastIcon(type)} me-2"></i>
                ${escapeHtml(message)}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toastContainer.appendChild(toastElement);
    
    // Show toast
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: type === 'error' ? 5000 : 3000
    });
    
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * Get Bootstrap background class for toast type
 */
function getToastBgClass(type) {
    const classMap = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'primary'
    };
    return classMap[type] || 'primary';
}

/**
 * Get Font Awesome icon for toast type
 */
function getToastIcon(type) {
    const iconMap = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return iconMap[type] || 'info-circle';
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .btn-animate {
        animation: pulse 0.6s ease-in-out;
    }
    
    .badge-animate {
        animation: bounce 0.3s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        80% { transform: translateY(-5px); }
    }
    
    .cart-item:hover {
        background-color: #f8f9fa;
        transition: background-color 0.2s ease;
    }
    
    .quantity-controls input::-webkit-outer-spin-button,
    .quantity-controls input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    
    .quantity-controls input[type=number] {
        -moz-appearance: textfield;
    }
`;
document.head.appendChild(style);

// Initialize cart on page load
loadCart();

/**
 * Proceed to checkout
 */
function proceedToCheckout() {
    if (cart.length === 0) {
        showToast('السلة فارغة', 'warning');
        return;
    }

    // Check if user is logged in
    if (!currentUser) {
        showToast('يرجى تسجيل الدخول أولاً', 'warning');
        showLoginModal();
        return;
    }

    // Show checkout modal
    showCheckoutModal();
}

/**
 * Show checkout modal
 */
function showCheckoutModal() {
    const subtotal = getCartTotal();
    const shipping = subtotal > 100 ? 0 : 10;
    const tax = subtotal * 0.1;
    const total = subtotal + shipping + tax;

    const checkoutHTML = `
        <div class="checkout-form">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="mb-3">معلومات الشحن</h5>
                    <form id="checkoutForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="fullName" value="${currentUser.firstName} ${currentUser.lastName}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="checkoutPhone" value="${currentUser.phone}" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="address" placeholder="الشارع والحي" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الرمز البريدي</label>
                                <input type="text" class="form-control" id="zipCode" required>
                            </div>
                        </div>

                        <h5 class="mb-3 mt-4">طريقة الدفع</h5>
                        <div class="payment-methods">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="paymentMethod" id="cod" value="cod" checked>
                                <label class="form-check-label" for="cod">
                                    <i class="fas fa-money-bill-wave ms-2"></i>الدفع عند الاستلام
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="paymentMethod" id="card" value="card">
                                <label class="form-check-label" for="card">
                                    <i class="fas fa-credit-card ms-2"></i>بطاقة ائتمان
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="paymentMethod" id="bank" value="bank">
                                <label class="form-check-label" for="bank">
                                    <i class="fas fa-university ms-2"></i>تحويل بنكي
                                </label>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-md-4">
                    <div class="order-summary-card">
                        <h5 class="mb-3">ملخص الطلب</h5>
                        <div class="order-items mb-3">
                            ${cart.map(item => `
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <img src="${item.image || 'https://via.placeholder.com/40'}"
                                             alt="${item.name}"
                                             class="rounded me-2"
                                             style="width: 40px; height: 40px; object-fit: cover;">
                                        <div>
                                            <div class="fw-bold small">${item.name}</div>
                                            <small class="text-muted">الكمية: ${item.quantity}</small>
                                        </div>
                                    </div>
                                    <span class="fw-bold">$${(item.price * item.quantity).toFixed(2)}</span>
                                </div>
                            `).join('')}
                        </div>

                        <hr>
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>$${subtotal.toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span>${shipping === 0 ? 'مجاني' : '$' + shipping.toFixed(2)}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة:</span>
                            <span>$${tax.toFixed(2)}</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold fs-5">
                            <span>الإجمالي:</span>
                            <span class="text-success">$${total.toFixed(2)}</span>
                        </div>

                        <button type="button" class="btn btn-success w-100 mt-3" onclick="completeOrder()">
                            <i class="fas fa-check ms-2"></i>تأكيد الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Create or update checkout modal
    let checkoutModal = document.getElementById('checkoutModal');
    if (!checkoutModal) {
        checkoutModal = document.createElement('div');
        checkoutModal.id = 'checkoutModal';
        checkoutModal.className = 'modal fade';
        checkoutModal.innerHTML = `
            <div class="modal-dialog modal-xl modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-shopping-cart ms-2"></i>إتمام الطلب
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="checkoutModalBody">
                        ${checkoutHTML}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(checkoutModal);
    } else {
        document.getElementById('checkoutModalBody').innerHTML = checkoutHTML;
    }

    const bsCheckoutModal = new bootstrap.Modal(checkoutModal);
    bsCheckoutModal.show();
}

/**
 * Complete order
 */
function completeOrder() {
    const form = document.getElementById('checkoutForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Get form data
    const fullName = document.getElementById('fullName').value.split(' ');
    const orderData = {
        firstName: fullName[0] || '',
        lastName: fullName.slice(1).join(' ') || '',
        email: currentUser.email,
        phone: document.getElementById('checkoutPhone').value,
        address: document.getElementById('address').value,
        city: document.getElementById('city').value,
        postalCode: document.getElementById('zipCode').value,
        shippingCountry: currentUser.countryName || 'السعودية',
        paymentMethod: document.querySelector('input[name="paymentMethod"]:checked').value
    };

    // Process order using new system
    const success = processOrder(orderData);

    if (success) {
        // Close cart modal if open
        const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
        if (cartModal) {
            cartModal.hide();
        }
    }
}

/**
 * Add to cart from product modal
 */
function addToCartFromModal(productId, productName, productPrice) {
    const quantityInput = document.getElementById('productQuantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;

    addToCart(productId, productName, productPrice, quantity);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) {
        modal.hide();
    }
}

/**
 * Buy now function
 */
function buyNow(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const quantityInput = document.getElementById('productQuantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;

    // Add to cart
    addToCart(productId, product.name, product.price, quantity);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    if (modal) {
        modal.hide();
    }

    // Proceed to checkout immediately
    setTimeout(() => {
        proceedToCheckout();
    }, 500);
}

/**
 * Show order success modal
 */
function showOrderSuccessModal(order) {
    const successHTML = `
        <div class="text-center">
            <div class="success-icon mb-4">
                <i class="fas fa-check-circle display-1 text-success"></i>
            </div>
            <h4 class="text-success mb-3">تم تأكيد طلبك بنجاح!</h4>
            <p class="mb-3">رقم الطلب: <strong>${order.id}</strong></p>
            <p class="mb-3">رقم التتبع: <strong>${order.trackingNumber}</strong></p>
            <p class="text-muted mb-4">سيتم التواصل معك قريباً لتأكيد التفاصيل</p>

            <div class="d-flex gap-2 justify-content-center">
                <button class="btn btn-primary" onclick="trackOrder('${order.id}')">
                    <i class="fas fa-truck ms-2"></i>تتبع الطلب
                </button>
                <button class="btn btn-outline-primary" onclick="showOrders()">
                    <i class="fas fa-list ms-2"></i>جميع الطلبات
                </button>
            </div>
        </div>
    `;

    // Create success modal
    let successModal = document.getElementById('orderSuccessModal');
    if (!successModal) {
        successModal = document.createElement('div');
        successModal.id = 'orderSuccessModal';
        successModal.className = 'modal fade';
        successModal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-body p-4">
                        ${successHTML}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(successModal);
    } else {
        successModal.querySelector('.modal-body').innerHTML = successHTML;
    }

    const bsSuccessModal = new bootstrap.Modal(successModal);
    bsSuccessModal.show();
}

/**
 * Sanitize user input to prevent XSS
 */
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/[<>]/g, '');
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

/**
 * Validate cart before checkout
 */
function validateCart() {
    if (cart.length === 0) {
        showToast('السلة فارغة', 'warning');
        return false;
    }

    // Check for invalid items
    const invalidItems = cart.filter(item =>
        !item.id || !item.name || !item.price || item.quantity <= 0
    );

    if (invalidItems.length > 0) {
        showToast('توجد منتجات غير صحيحة في السلة', 'danger');
        return false;
    }

    return true;
}

/**
 * Process order and save to database
 */
function processOrder(orderData) {
    // Check if user is logged in
    if (!currentUser) {
        showToast('يجب تسجيل الدخول لإتمام الطلب', 'warning');
        return false;
    }

    // Validate cart
    if (!validateCart()) {
        return false;
    }

    try {
        // Prepare order data
        const order = {
            userId: currentUser.id,
            items: cart.map(item => ({
                id: item.id,
                name: item.name,
                price: item.price,
                quantity: item.quantity,
                image: item.image || '',
                total: item.price * item.quantity
            })),
            total: getCartTotal(),
            customerInfo: {
                firstName: orderData.firstName || currentUser.firstName,
                lastName: orderData.lastName || currentUser.lastName,
                email: orderData.email || currentUser.email,
                phone: orderData.phone || currentUser.phone,
                country: currentUser.country || 'SA',
                countryName: currentUser.countryName || 'السعودية'
            },
            shippingAddress: {
                address: orderData.address || '',
                city: orderData.city || '',
                postalCode: orderData.postalCode || '',
                country: orderData.shippingCountry || currentUser.countryName || 'السعودية'
            },
            paymentMethod: orderData.paymentMethod || 'cash_on_delivery'
        };

        // Save order to database
        const result = secureDB.createOrder(order);

        if (result.success) {
            // Clear cart
            clearCart();

            // Show success message
            showToast(`تم إنشاء الطلب بنجاح! رقم الطلب: ${result.order.orderNumber}`, 'success');

            // Close checkout modal
            const checkoutModal = document.getElementById('checkoutModal');
            if (checkoutModal) {
                const modal = bootstrap.Modal.getInstance(checkoutModal);
                if (modal) {
                    modal.hide();
                }
            }

            // Show order details
            showOrderConfirmation(result.order);

            return true;
        } else {
            showToast(result.error || 'حدث خطأ أثناء إنشاء الطلب', 'danger');
            return false;
        }
    } catch (e) {
        console.error('Error processing order:', e);
        showToast('حدث خطأ أثناء معالجة الطلب', 'danger');
        return false;
    }
}

/**
 * Show order confirmation
 */
function showOrderConfirmation(order) {
    const confirmationHTML = `
        <div class="order-confirmation text-center">
            <div class="success-icon mb-4">
                <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
            </div>
            <h4 class="text-success mb-3">تم إنشاء طلبك بنجاح!</h4>
            <div class="order-details">
                <p><strong>رقم الطلب:</strong> ${order.orderNumber}</p>
                <p><strong>إجمالي المبلغ:</strong> $${order.total.toFixed(2)}</p>
                <p><strong>تاريخ الطلب:</strong> ${new Date(order.createdAt).toLocaleDateString('ar-SA')}</p>
                <p><strong>حالة الطلب:</strong> <span class="badge bg-warning">قيد المعالجة</span></p>
            </div>
            <div class="mt-4">
                <button class="btn btn-primary me-2" onclick="showOrders()">
                    <i class="fas fa-list ms-1"></i>عرض جميع الطلبات
                </button>
                <button class="btn btn-outline-primary" onclick="closeOrderConfirmation()">
                    <i class="fas fa-shopping-bag ms-1"></i>متابعة التسوق
                </button>
            </div>
        </div>
    `;

    // Create confirmation modal
    const confirmationModal = document.createElement('div');
    confirmationModal.className = 'modal fade';
    confirmationModal.id = 'orderConfirmationModal';
    confirmationModal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    ${confirmationHTML}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmationModal);

    const modal = new bootstrap.Modal(confirmationModal);
    modal.show();

    // Remove modal after hiding
    confirmationModal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(confirmationModal);
    });
}

/**
 * Close order confirmation
 */
function closeOrderConfirmation() {
    const modal = document.getElementById('orderConfirmationModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

/**
 * Show user orders
 */
function showOrders() {
    if (!currentUser) {
        showToast('يجب تسجيل الدخول لعرض الطلبات', 'warning');
        return;
    }

    const orders = secureDB.getUserOrders(currentUser.id);

    let ordersHTML = '';
    if (orders.length === 0) {
        ordersHTML = `
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag display-4 text-muted mb-3"></i>
                <h4>لا توجد طلبات</h4>
                <p class="text-muted">لم تقم بأي طلبات حتى الآن</p>
                <button class="btn btn-primary" onclick="closeOrdersModal()">
                    <i class="fas fa-shopping-cart ms-1"></i>ابدأ التسوق
                </button>
            </div>
        `;
    } else {
        ordersHTML = orders.map(order => `
            <div class="order-item mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <strong>طلب رقم: ${order.orderNumber}</strong>
                            <small class="text-muted ms-2">${new Date(order.createdAt).toLocaleDateString('ar-SA')}</small>
                        </div>
                        <span class="badge ${getStatusBadgeClass(order.status)}">${getStatusText(order.status)}</span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>المنتجات:</h6>
                                <ul class="list-unstyled">
                                    ${order.items.map(item => `
                                        <li class="mb-1">
                                            <i class="fas fa-box text-primary ms-1"></i>
                                            ${item.name} × ${item.quantity} - $${(item.price * item.quantity).toFixed(2)}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                            <div class="col-md-4 text-end">
                                <h5 class="text-primary">الإجمالي: $${order.total.toFixed(2)}</h5>
                                <small class="text-muted">طريقة الدفع: ${getPaymentMethodText(order.paymentMethod)}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Create orders modal
    const ordersModal = document.createElement('div');
    ordersModal.className = 'modal fade';
    ordersModal.id = 'ordersModal';
    ordersModal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-list ms-2"></i>طلباتي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${ordersHTML}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(ordersModal);

    const modal = new bootstrap.Modal(ordersModal);
    modal.show();

    // Remove modal after hiding
    ordersModal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(ordersModal);
    });
}

/**
 * Get status badge class
 */
function getStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-warning';
        case 'processing': return 'bg-info';
        case 'shipped': return 'bg-primary';
        case 'delivered': return 'bg-success';
        case 'cancelled': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

/**
 * Get status text
 */
function getStatusText(status) {
    switch (status) {
        case 'pending': return 'قيد المعالجة';
        case 'processing': return 'جاري التحضير';
        case 'shipped': return 'تم الشحن';
        case 'delivered': return 'تم التسليم';
        case 'cancelled': return 'ملغي';
        default: return 'غير محدد';
    }
}

/**
 * Get payment method text
 */
function getPaymentMethodText(method) {
    switch (method) {
        case 'cash_on_delivery': return 'الدفع عند الاستلام';
        case 'credit_card': return 'بطاقة ائتمان';
        case 'bank_transfer': return 'تحويل بنكي';
        default: return 'غير محدد';
    }
}

/**
 * Close orders modal
 */
function closeOrdersModal() {
    const modal = document.getElementById('ordersModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

// Export cart functions for global access
window.Cart = {
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    showCart,
    getCartTotal,
    getCartItemCount,
    proceedToCheckout,
    validateCart,
    formatCurrency
};
