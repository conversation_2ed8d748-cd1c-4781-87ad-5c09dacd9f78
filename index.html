<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Easy Store - أفضل المنتجات التقنية</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Cairo Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/custom.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold animated-brand" href="#">
                <div class="brand-container">
                    <div class="store-icon-3d">
                        <span class="store-logo">ES</span>
                        <div class="icon-glow"></div>
                    </div>
                    <span class="brand-text">Easy Store</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Search Section - Center -->
                <div class="mx-auto">
                    <div class="search-section">
                        <div class="search-wrapper">
                            <input type="text" class="search-input" id="searchInput" placeholder="ابحث عن المنتجات...">
                            <button class="search-button" type="button" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Right Side Actions - Cart & User -->
                <div class="d-flex align-items-center navbar-actions">
                    <!-- User Account Section -->
                    <div class="user-section me-3">
                        <div class="user-menu" id="userMenu">
                            <!-- Logged out state -->
                            <div class="logged-out-state" id="loggedOutState">
                                <button class="btn btn-outline-light me-2" onclick="showLoginModal()">
                                    <i class="fas fa-sign-in-alt ms-1"></i>دخول
                                </button>
                                <button class="btn btn-light" onclick="showRegisterModal()">
                                    <i class="fas fa-user-plus ms-1"></i>تسجيل
                                </button>
                            </div>

                            <!-- Logged in state -->
                            <div class="logged-in-state d-none" id="loggedInState">
                                <div class="dropdown">
                                    <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user ms-1"></i>
                                        <span id="userName">المستخدم</span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="#" onclick="showProfile()">
                                            <i class="fas fa-user ms-2"></i>الملف الشخصي
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showOrders()">
                                            <i class="fas fa-shopping-bag ms-2"></i>طلباتي
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="showWishlist()">
                                            <i class="fas fa-heart ms-2"></i>المفضلة
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="logout()">
                                            <i class="fas fa-sign-out-alt ms-2"></i>تسجيل خروج
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cart Section -->
                    <div class="cart-section">
                        <button class="cart-button" id="cartBtn">
                            <div class="cart-icon-wrapper">
                                <i class="fas fa-shopping-cart"></i>
                                <span class="cart-count" id="cartCount">0</span>
                            </div>
                            <span class="cart-text"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section bg-light py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <div class="hero-badge mb-3">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-star ms-1"></i>
                            متجر موثوق ومضمون
                        </span>
                    </div>
                    <h1 class="display-4 fw-bold text-primary mb-4 gradient-text">
                        مرحباً بك في
                        <span class="text-gradient">Easy Store</span>
                    </h1>
                    <p class="lead mb-4 text-secondary">
                        متجرك المتخصص في البرامج والتفعيلات، أجهزة الفحص والقياس، أدوات اللحام، المكونات الإلكترونية، والأجهزة المستعملة عالية الجودة. خبرة وثقة منذ سنوات.
                    </p>
                    <div class="hero-stats mb-4">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="text-primary mb-1">50+</h4>
                                    <small class="text-muted">منتج متخصص</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="text-success mb-1">5</h4>
                                    <small class="text-muted">فئات متخصصة</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="text-info mb-1">100%</h4>
                                    <small class="text-muted">ضمان الجودة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hero-actions">
                        <button class="btn btn-primary btn-lg pulse-element shop-now-btn" onclick="scrollToProducts()">
                            <div class="shop-icon-3d">
                                <i class="fas fa-shopping-bag"></i>
                                <div class="icon-sparkles">
                                    <span class="sparkle sparkle-1"></span>
                                    <span class="sparkle sparkle-2"></span>
                                    <span class="sparkle sparkle-3"></span>
                                </div>
                            </div>
                            <span class="shop-text">تسوق الآن</span>
                        </button>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image text-center position-relative">
                        <div class="hero-circle-1"></div>
                        <div class="hero-circle-2"></div>
                        <i class="fas fa-shopping-cart display-1 text-primary position-relative"></i>
                        <div class="hero-floating-elements">
                            <div class="floating-icon floating-icon-1">
                                <i class="fas fa-laptop"></i>
                            </div>
                            <div class="floating-icon floating-icon-2">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <div class="floating-icon floating-icon-3">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="floating-icon floating-icon-4">
                                <i class="fas fa-home"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Promotions Banner -->
    <section class="promotions-banner py-3 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-fire text-warning fs-4 ms-3"></i>
                        <div>
                            <strong>عرض محدود!</strong>
                            <span class="ms-2">خصم يصل إلى 40% على جميع المنتجات + شحن مجاني للطلبات أكثر من 100$</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="countdown-timer">
                        <small>ينتهي العرض خلال:</small>
                        <span class="fw-bold" id="countdown">30 يوم</span>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Products Section -->
    <section id="products" class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-md-6">
                    <h2 class="display-6 fw-bold">جميع المنتجات</h2>
                    <p class="text-muted">تصفح مجموعتنا الكاملة من المنتجات المتنوعة</p>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center">
                        <select class="form-select w-auto" id="sortSelect">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="rating">التقييم الأعلى</option>
                            <option value="newest">الأحدث</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Advanced Filters -->
            <!-- Category Cards Section -->
            <div class="category-cards-section mb-5">
                <div class="container">
                    <div class="row justify-content-center g-4">
                        <!-- Software Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card active" data-filter="software" data-color="#4facfe">
                                <div class="category-icon">
                                    <i class="fas fa-code"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">البرامج والتفعيلات</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>

                        <!-- Measurement Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card" data-filter="measurement" data-color="#43e97b">
                                <div class="category-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">أجهزة الفحص والقياس</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>

                        <!-- Welding Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card" data-filter="welding" data-color="#ff6b6b">
                                <div class="category-icon">
                                    <i class="fas fa-fire"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">أدوات اللحام</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>

                        <!-- Electronics Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card" data-filter="electronics" data-color="#feca57">
                                <div class="category-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">مكونات إلكترونية</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>

                        <!-- Programmers Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card" data-filter="programmers" data-color="#ff9a9e">
                                <div class="category-icon">
                                    <i class="fas fa-memory"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">مبرمجات</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>

                        <!-- Used Card -->
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="category-card" data-filter="used" data-color="#a8edea">
                                <div class="category-icon">
                                    <i class="fas fa-recycle"></i>
                                </div>
                                <div class="category-info">
                                    <h6 class="category-title">مستعمل</h6>
                                </div>
                                <div class="category-overlay"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-container">
                <div class="row g-4" id="productsGrid">
                    <!-- Products will be loaded dynamically -->
                </div>

                <!-- Loading Spinner -->
                <div class="text-center py-5" id="loadingSpinner" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل المنتجات...</p>
                </div>

                <!-- No Results Message -->
                <div class="text-center py-5" id="noResults" style="display: none;">
                    <i class="fas fa-search display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">لم يتم العثور على منتجات</h5>
                    <p class="text-muted">جرب تغيير معايير البحث أو الفلترة</p>
                </div>
            </div>


        </div>
    </section>



    <!-- Shopping Cart Modal -->
    <div class="modal fade" id="cartModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سلة التسوق</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="cartItems">
                        <!-- Cart items will be loaded here -->
                    </div>
                    <div class="cart-empty text-center py-5" id="cartEmpty">
                        <i class="fas fa-shopping-cart display-4 text-muted mb-3"></i>
                        <h5>سلة التسوق فارغة</h5>
                        <p class="text-muted">أضف بعض المنتجات للبدء!</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="w-100">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>الإجمالي: $<span id="cartTotal">0.00</span></h5>
                        </div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">متابعة التسوق</button>
                        <button type="button" class="btn btn-primary" id="checkoutBtn">
                            <i class="fas fa-credit-card ms-2"></i>إتمام الشراء
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Detail Modal -->
    <div class="modal fade" id="productModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productModalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="productModalBody">
                    <!-- Product details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-sign-in-alt ms-2"></i>تسجيل الدخول
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="loginEmail" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">تذكرني</label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt ms-2"></i>دخول
                        </button>
                    </form>
                    <div class="text-center mt-3">
                        <p>ليس لديك حساب؟ <a href="#" onclick="showRegisterModal()">سجل الآن</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus ms-2"></i>إنشاء حساب جديد
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">الاسم الأول</label>
                                <input type="text" class="form-control" id="firstName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">اسم العائلة</label>
                                <input type="text" class="form-control" id="lastName" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="registerEmail" required>
                        </div>
                        <div class="mb-3 country-select">
                            <label for="country" class="form-label">
                                <i class="fas fa-globe"></i>دولة الإقامة
                            </label>
                            <select class="form-select" id="country" required>
                                <option value="">اختر دولة الإقامة</option>
                                <!-- Arab Countries -->
                                <optgroup label="الدول العربية">
                                    <option value="SA" data-code="+966" data-pattern="^5[0-9]{8}$" data-placeholder="5xxxxxxxx">السعودية (+966)</option>
                                    <option value="AE" data-code="+971" data-pattern="^5[0-9]{8}$" data-placeholder="5xxxxxxxx">الإمارات (+971)</option>
                                    <option value="EG" data-code="+20" data-pattern="^1[0-9]{9}$" data-placeholder="1xxxxxxxxx">مصر (+20)</option>
                                    <option value="JO" data-code="+962" data-pattern="^7[0-9]{8}$" data-placeholder="7xxxxxxxx">الأردن (+962)</option>
                                    <option value="LB" data-code="+961" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">لبنان (+961)</option>
                                    <option value="SY" data-code="+963" data-pattern="^9[0-9]{8}$" data-placeholder="9xxxxxxxx">سوريا (+963)</option>
                                    <option value="IQ" data-code="+964" data-pattern="^7[0-9]{9}$" data-placeholder="7xxxxxxxxx">العراق (+964)</option>
                                    <option value="KW" data-code="+965" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">الكويت (+965)</option>
                                    <option value="QA" data-code="+974" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">قطر (+974)</option>
                                    <option value="BH" data-code="+973" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">البحرين (+973)</option>
                                    <option value="OM" data-code="+968" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">عمان (+968)</option>
                                    <option value="YE" data-code="+967" data-pattern="^7[0-9]{8}$" data-placeholder="7xxxxxxxx">اليمن (+967)</option>
                                    <option value="PS" data-code="+970" data-pattern="^5[0-9]{8}$" data-placeholder="5xxxxxxxx">فلسطين (+970)</option>
                                    <option value="MA" data-code="+212" data-pattern="^[0-9]{9}$" data-placeholder="xxxxxxxxx">المغرب (+212)</option>
                                    <option value="DZ" data-code="+213" data-pattern="^[0-9]{9}$" data-placeholder="xxxxxxxxx">الجزائر (+213)</option>
                                    <option value="TN" data-code="+216" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">تونس (+216)</option>
                                    <option value="LY" data-code="+218" data-pattern="^9[0-9]{8}$" data-placeholder="9xxxxxxxx">ليبيا (+218)</option>
                                    <option value="SD" data-code="+249" data-pattern="^9[0-9]{8}$" data-placeholder="9xxxxxxxx">السودان (+249)</option>
                                    <option value="SO" data-code="+252" data-pattern="^[0-9]{8,9}$" data-placeholder="xxxxxxxx">الصومال (+252)</option>
                                    <option value="DJ" data-code="+253" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">جيبوتي (+253)</option>
                                    <option value="KM" data-code="+269" data-pattern="^[0-9]{7}$" data-placeholder="xxxxxxx">جزر القمر (+269)</option>
                                    <option value="MR" data-code="+222" data-pattern="^[0-9]{8}$" data-placeholder="xxxxxxxx">موريتانيا (+222)</option>
                                </optgroup>
                                <!-- Major International Countries -->
                                <optgroup label="الدول الأجنبية الرئيسية">
                                    <option value="US" data-code="+1" data-pattern="^[0-9]{10}$" data-placeholder="xxxxxxxxxx">الولايات المتحدة (+1)</option>
                                    <option value="CA" data-code="+1" data-pattern="^[0-9]{10}$" data-placeholder="xxxxxxxxxx">كندا (+1)</option>
                                    <option value="GB" data-code="+44" data-pattern="^7[0-9]{9}$" data-placeholder="7xxxxxxxxx">المملكة المتحدة (+44)</option>
                                    <option value="FR" data-code="+33" data-pattern="^[0-9]{9}$" data-placeholder="xxxxxxxxx">فرنسا (+33)</option>
                                    <option value="DE" data-code="+49" data-pattern="^1[0-9]{10}$" data-placeholder="1xxxxxxxxxx">ألمانيا (+49)</option>
                                    <option value="IT" data-code="+39" data-pattern="^3[0-9]{9}$" data-placeholder="3xxxxxxxxx">إيطاليا (+39)</option>
                                    <option value="ES" data-code="+34" data-pattern="^[0-9]{9}$" data-placeholder="xxxxxxxxx">إسبانيا (+34)</option>
                                    <option value="RU" data-code="+7" data-pattern="^9[0-9]{9}$" data-placeholder="9xxxxxxxxx">روسيا (+7)</option>
                                    <option value="CN" data-code="+86" data-pattern="^1[0-9]{10}$" data-placeholder="1xxxxxxxxxx">الصين (+86)</option>
                                    <option value="JP" data-code="+81" data-pattern="^[0-9]{10,11}$" data-placeholder="xxxxxxxxxx">اليابان (+81)</option>
                                    <option value="KR" data-code="+82" data-pattern="^1[0-9]{9}$" data-placeholder="1xxxxxxxxx">كوريا الجنوبية (+82)</option>
                                    <option value="IN" data-code="+91" data-pattern="^[0-9]{10}$" data-placeholder="xxxxxxxxxx">الهند (+91)</option>
                                    <option value="PK" data-code="+92" data-pattern="^3[0-9]{9}$" data-placeholder="3xxxxxxxxx">باكستان (+92)</option>
                                    <option value="BD" data-code="+880" data-pattern="^1[0-9]{9}$" data-placeholder="1xxxxxxxxx">بنغلاديش (+880)</option>
                                    <option value="TR" data-code="+90" data-pattern="^5[0-9]{9}$" data-placeholder="5xxxxxxxxx">تركيا (+90)</option>
                                    <option value="IR" data-code="+98" data-pattern="^9[0-9]{9}$" data-placeholder="9xxxxxxxxx">إيران (+98)</option>
                                    <option value="AF" data-code="+93" data-pattern="^7[0-9]{8}$" data-placeholder="7xxxxxxxx">أفغانستان (+93)</option>
                                    <option value="AU" data-code="+61" data-pattern="^4[0-9]{8}$" data-placeholder="4xxxxxxxx">أستراليا (+61)</option>
                                    <option value="NZ" data-code="+64" data-pattern="^2[0-9]{8}$" data-placeholder="2xxxxxxxx">نيوزيلندا (+64)</option>
                                    <option value="BR" data-code="+55" data-pattern="^[0-9]{10,11}$" data-placeholder="xxxxxxxxxxx">البرازيل (+55)</option>
                                    <option value="MX" data-code="+52" data-pattern="^[0-9]{10}$" data-placeholder="xxxxxxxxxx">المكسيك (+52)</option>
                                    <option value="AR" data-code="+54" data-pattern="^9[0-9]{9}$" data-placeholder="9xxxxxxxxx">الأرجنتين (+54)</option>
                                    <option value="ZA" data-code="+27" data-pattern="^[0-9]{9}$" data-placeholder="xxxxxxxxx">جنوب أفريقيا (+27)</option>
                                    <option value="NG" data-code="+234" data-pattern="^[0-9]{10}$" data-placeholder="xxxxxxxxxx">نيجيريا (+234)</option>
                                    <option value="KE" data-code="+254" data-pattern="^7[0-9]{8}$" data-placeholder="7xxxxxxxx">كينيا (+254)</option>
                                    <option value="ET" data-code="+251" data-pattern="^9[0-9]{8}$" data-placeholder="9xxxxxxxx">إثيوبيا (+251)</option>
                                </optgroup>
                            </select>
                        </div>
                        <div class="mb-3 phone-input-group">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone"></i>رقم الهاتف
                            </label>
                            <div class="input-group">
                                <span class="input-group-text" id="countryCode">+966</span>
                                <input type="tel" class="form-control" id="phone" placeholder="5xxxxxxxx" required>
                            </div>
                            <div class="form-text phone-help" id="phoneHelp">
                                <i class="fas fa-info-circle"></i>
                                أدخل رقم الهاتف بدون رمز الدولة (+966)
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="registerPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>

                        <!-- Profile Avatar Selection -->
                        <div class="mb-4">
                            <label class="form-label">اختر أيقونة الملف الشخصي</label>
                            <div class="avatar-selection">
                                <div class="row g-2">
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user-tie">
                                            <i class="fas fa-user-tie"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user-graduate">
                                            <i class="fas fa-user-graduate"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user-cog">
                                            <i class="fas fa-user-cog"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user-astronaut">
                                            <i class="fas fa-user-astronaut"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="user-ninja">
                                            <i class="fas fa-user-ninja"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="row g-2 mt-1">
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="robot">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="cat">
                                            <i class="fas fa-cat"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="dragon">
                                            <i class="fas fa-dragon"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="rocket">
                                            <i class="fas fa-rocket"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="crown">
                                            <i class="fas fa-crown"></i>
                                        </div>
                                    </div>
                                    <div class="col-2">
                                        <div class="avatar-option" data-avatar="gem">
                                            <i class="fas fa-gem"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selectedAvatar" value="user">
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                أوافق على <a href="#" onclick="showTerms()">الشروط والأحكام</a>
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-user-plus ms-2"></i>إنشاء الحساب
                        </button>
                    </form>
                    <div class="text-center mt-3">
                        <p>لديك حساب بالفعل؟ <a href="#" onclick="showLoginModal()">سجل دخول</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user ms-2"></i>الملف الشخصي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="profileModalBody">
                    <!-- Profile content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Modal -->
    <div class="modal fade" id="ordersModal" tabindex="-1">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-shopping-bag ms-2"></i>طلباتي
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="ordersModalBody">
                    <!-- Orders content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Contact Button -->
    <div class="floating-contact">
        <div class="floating-contact-btn" id="floatingContactBtn">
            <i class="fas fa-comments"></i>
        </div>
        <div class="floating-contact-menu" id="floatingContactMenu">
            <a href="https://wa.me/966551234567" target="_blank" class="contact-option whatsapp" title="واتساب">
                <i class="fab fa-whatsapp"></i>
                <span>واتساب</span>
            </a>
            <a href="https://t.me/yourusername" target="_blank" class="contact-option telegram" title="تليجرام">
                <i class="fab fa-telegram-plane"></i>
                <span>تليجرام</span>
            </a>
            <a href="https://facebook.com/yourpage" target="_blank" class="contact-option facebook" title="فيسبوك">
                <i class="fab fa-facebook-f"></i>
                <span>فيسبوك</span>
            </a>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <!-- Payment Methods -->
                <div class="col-12">
                    <div class="text-center">
                        <h6 class="mb-3 text-primary">طرق الدفع المتاحة</h6>
                        <div class="payment-methods d-flex flex-wrap justify-content-center gap-3">
                            <!-- E-Wallets -->
                            <div class="payment-item" title="المحافظ الإلكترونية">
                                <i class="fas fa-wallet text-success fs-4"></i>
                                <small class="d-block text-muted">محافظ إلكترونية</small>
                            </div>

                            <!-- PayPal -->
                            <div class="payment-item" title="PayPal">
                                <i class="fab fa-paypal text-primary fs-4"></i>
                                <small class="d-block text-muted">PayPal</small>
                            </div>

                            <!-- Bank Transfer -->
                            <div class="payment-item" title="تحويل بنكي">
                                <i class="fas fa-university text-info fs-4"></i>
                                <small class="d-block text-muted">تحويل بنكي</small>
                            </div>

                            <!-- USDT -->
                            <div class="payment-item" title="USDT">
                                <i class="fab fa-bitcoin text-warning fs-4"></i>
                                <small class="d-block text-muted">USDT</small>
                            </div>

                            <!-- Credit Cards -->
                            <div class="payment-item" title="بطاقات ائتمان">
                                <i class="fas fa-credit-card text-light fs-4"></i>
                                <small class="d-block text-muted">بطاقات ائتمان</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Copyright -->
            <hr class="my-3 border-secondary">
            <div class="row justify-content-center">
                <div class="col-12 text-center">
                    <p class="mb-2 text-muted small">
                        &copy; 2025 Easy Store. جميع الحقوق محفوظة.
                    </p>
                    <p class="mb-0 text-muted small">
                        <i class="fas fa-shield-alt text-success ms-1"></i>
                        دفع آمن ومضمون
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JavaScript -->
    <script src="assets/js/database.js"></script>
    <script src="assets/js/ecommerce.js"></script>
    <script src="assets/js/cart.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/orders.js"></script>
</body>
</html>
