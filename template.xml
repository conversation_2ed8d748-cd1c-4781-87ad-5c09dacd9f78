<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:css='false' b:defaultwidgetversion='2' b:layoutsVersion='3' b:responsive='true' b:templateUrl='estorepro.blogspot.com' b:templateVersion='1.0.0' expr:dir='data:blog.languageDirection' expr:lang='data:blog.locale' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
    <meta charset='UTF-8'/>
    <meta content='width=device-width, initial-scale=1.0' name='viewport'/>
    
    <!-- SEO Meta Tags -->
    <b:if cond='data:view.isPost'>
        <title><data:blog.pageName/> | <data:blog.title/></title>
        <meta expr:content='data:post.snippet' name='description'/>
    <b:else/>
        <title><data:blog.pageTitle/></title>
        <meta expr:content='data:blog.metaDescription' name='description'/>
    </b:if>
    
    <!-- Open Graph Meta Tags -->
    <meta expr:content='data:blog.title' property='og:site_name'/>
    <meta expr:content='data:blog.canonicalUrl' property='og:url'/>
    <meta content='website' property='og:type'/>
    
    <!-- Bootstrap 5 CSS -->
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'/>
    <!-- Font Awesome Icons -->
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
    
    <!-- Custom CSS -->
    <b:skin><![CDATA[
        /* Custom E-Commerce Styles */
        :root {
            --primary-color: #0d6efd;
            --secondary-color: #6c757d;
            --success-color: #198754;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #0dcaf0;
            --light-color: #f8f9fa;
            --dark-color: #212529;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .hero-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 400px;
        }

        .category-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .product-card {
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .product-image {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: var(--primary-color);
        }

        .price {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--success-color);
        }

        .original-price {
            text-decoration: line-through;
            color: var(--secondary-color);
            font-size: 1rem;
        }

        .rating {
            color: #ffc107;
        }

        .search-container {
            width: 300px;
        }

        .hover-shadow:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .social-links a {
            font-size: 1.2rem;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: var(--primary-color) !important;
        }

        .cart-item {
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 0;
        }

        .cart-item:last-child {
            border-bottom: none;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
        }

        .quantity-controls button {
            width: 30px;
            height: 30px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            margin: 0 5px;
        }

        .filter-buttons .btn {
            margin-bottom: 10px;
        }

        .filter-buttons .btn.active {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .post-body {
            padding: 2rem 0;
        }

        .blog-post {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            padding: 2rem;
        }

        .blog-post h1 {
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .post-meta {
            color: var(--secondary-color);
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .post-meta i {
            margin-right: 5px;
        }

        @media (max-width: 768px) {
            .search-container {
                width: 200px;
            }
            
            .hero-section h1 {
                font-size: 2rem;
            }
            
            .filter-buttons {
                text-align: center;
            }
        }
    ]]></b:skin>
</head>

<body>
    <!-- Skip to content link for accessibility -->
    <a class='visually-hidden-focusable' href='#main'>Skip to main content</a>

    <!-- Navigation -->
    <nav class='navbar navbar-expand-lg navbar-dark bg-primary sticky-top'>
        <div class='container'>
            <a class='navbar-brand fw-bold' href='/' title='Home'>
                <i class='fas fa-store me-2'></i><data:blog.title/>
            </a>
            <button class='navbar-toggler' type='button' data-bs-toggle='collapse' data-bs-target='#navbarNav' aria-controls='navbarNav' aria-expanded='false' aria-label='Toggle navigation'>
                <span class='navbar-toggler-icon'></span>
            </button>
            <div class='collapse navbar-collapse' id='navbarNav'>
                <ul class='navbar-nav me-auto'>
                    <li class='nav-item'>
                        <a class='nav-link' href='/' title='Home'>Home</a>
                    </li>
                    <li class='nav-item dropdown'>
                        <a class='nav-link dropdown-toggle' href='#' role='button' data-bs-toggle='dropdown' aria-expanded='false'>
                            Categories
                        </a>
                        <ul class='dropdown-menu'>
                            <li><a class='dropdown-item' href='/search/label/Electronics' title='Electronics'>Electronics</a></li>
                            <li><a class='dropdown-item' href='/search/label/Clothing' title='Clothing'>Clothing</a></li>
                            <li><a class='dropdown-item' href='/search/label/Books' title='Books'>Books</a></li>
                            <li><a class='dropdown-item' href='/search/label/Home' title='Home &amp; Garden'>Home &amp; Garden</a></li>
                        </ul>
                    </li>
                    <li class='nav-item'>
                        <a class='nav-link' href='#contact' title='Contact'>Contact</a>
                    </li>
                </ul>
                <div class='d-flex align-items-center'>
                    <div class='search-container me-3'>
                        <div class='input-group'>
                            <input type='text' class='form-control' id='searchInput' placeholder='Search products...' aria-label='Search products'/>
                            <button class='btn btn-outline-light' type='button' id='searchBtn' aria-label='Search'>
                                <i class='fas fa-search'></i>
                            </button>
                        </div>
                    </div>
                    <button class='btn btn-outline-light position-relative me-2' id='cartBtn' aria-label='Shopping cart'>
                        <i class='fas fa-shopping-cart'></i>
                        <span class='position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger' id='cartCount'>
                            0
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id='main'>
        <b:section class='main' id='main' maxwidgets='1' showaddelement='no'>
            <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='2' visible='true'>
                <b:widget-settings>
                    <b:widget-setting name='showDateHeader'>false</b:widget-setting>
                    <b:widget-setting name='showShareButtons'>true</b:widget-setting>
                    <b:widget-setting name='postLabelsLabel'>Labels:</b:widget-setting>
                    <b:widget-setting name='showCommentLink'>true</b:widget-setting>
                    <b:widget-setting name='showLocationLabel'>Location:</b:widget-setting>
                    <b:widget-setting name='postLocationLabel'>Location:</b:widget-setting>
                    <b:widget-setting name='showTimestamp'>true</b:widget-setting>
                    <b:widget-setting name='postsPerAd'>5</b:widget-setting>
                    <b:widget-setting name='showBacklinks'>false</b:widget-setting>
                    <b:widget-setting name='showInlineAds'>false</b:widget-setting>
                    <b:widget-setting name='showReactions'>false</b:widget-setting>
                </b:widget-settings>
                <b:includable id='main'>
                    <b:if cond='data:view.isHomepage'>
                        <!-- Homepage Content -->
                        <div class='homepage-content'>
                            <!-- Hero Section -->
                            <section class='hero-section bg-light py-5'>
                                <div class='container'>
                                    <div class='row align-items-center'>
                                        <div class='col-lg-6'>
                                            <h1 class='display-4 fw-bold text-primary mb-4'>Welcome to <data:blog.title/></h1>
                                            <p class='lead mb-4'>Discover amazing products at unbeatable prices. Shop with confidence and enjoy fast delivery.</p>
                                            <button class='btn btn-primary btn-lg' onclick='scrollToProducts()'>
                                                <i class='fas fa-shopping-bag me-2'></i>Shop Now
                                            </button>
                                        </div>
                                        <div class='col-lg-6'>
                                            <div class='hero-image text-center'>
                                                <i class='fas fa-shopping-cart display-1 text-primary'></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Featured Categories -->
                            <section class='py-5'>
                                <div class='container'>
                                    <h2 class='text-center mb-5'>Shop by Category</h2>
                                    <div class='row g-4'>
                                        <div class='col-md-3'>
                                            <div class='category-card text-center p-4 border rounded hover-shadow' onclick='location.href="/search/label/Electronics"'>
                                                <i class='fas fa-laptop display-5 text-primary mb-3'></i>
                                                <h5>Electronics</h5>
                                                <p class='text-muted'>Latest gadgets and devices</p>
                                            </div>
                                        </div>
                                        <div class='col-md-3'>
                                            <div class='category-card text-center p-4 border rounded hover-shadow' onclick='location.href="/search/label/Clothing"'>
                                                <i class='fas fa-tshirt display-5 text-primary mb-3'></i>
                                                <h5>Clothing</h5>
                                                <p class='text-muted'>Fashion for everyone</p>
                                            </div>
                                        </div>
                                        <div class='col-md-3'>
                                            <div class='category-card text-center p-4 border rounded hover-shadow' onclick='location.href="/search/label/Books"'>
                                                <i class='fas fa-book display-5 text-primary mb-3'></i>
                                                <h5>Books</h5>
                                                <p class='text-muted'>Knowledge and entertainment</p>
                                            </div>
                                        </div>
                                        <div class='col-md-3'>
                                            <div class='category-card text-center p-4 border rounded hover-shadow' onclick='location.href="/search/label/Home"'>
                                                <i class='fas fa-home display-5 text-primary mb-3'></i>
                                                <h5>Home &amp; Garden</h5>
                                                <p class='text-muted'>Beautiful living spaces</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>

                            <!-- Recent Products/Posts -->
                            <section id='products' class='py-5 bg-light'>
                                <div class='container'>
                                    <h2 class='text-center mb-5'>Featured Products</h2>
                                    <div class='row g-4'>
                                        <b:loop values='data:posts' var='post'>
                                            <div class='col-lg-4 col-md-6 mb-4'>
                                                <article class='product-card h-100 bg-white border-0 shadow-sm'>
                                                    <div class='product-image'>
                                                        <b:if cond='data:post.featuredImage'>
                                                            <img expr:alt='data:post.title' expr:src='data:post.featuredImage' class='card-img-top' style='height: 200px; object-fit: cover;'/>
                                                        <b:else/>
                                                            <i class='fas fa-shopping-bag'></i>
                                                        </b:if>
                                                    </div>
                                                    <div class='card-body d-flex flex-column'>
                                                        <h5 class='card-title'><data:post.title/></h5>
                                                        <p class='card-text flex-grow-1'><data:post.snippet/></p>
                                                        <div class='d-flex justify-content-between align-items-center'>
                                                            <div class='price-info'>
                                                                <span class='price'>$99.99</span>
                                                            </div>
                                                            <div class='rating'>
                                                                <i class='fas fa-star'></i>
                                                                <i class='fas fa-star'></i>
                                                                <i class='fas fa-star'></i>
                                                                <i class='fas fa-star'></i>
                                                                <i class='far fa-star'></i>
                                                                <small class='text-muted'>(4.0)</small>
                                                            </div>
                                                        </div>
                                                        <div class='mt-3'>
                                                            <a expr:href='data:post.url' class='btn btn-outline-primary me-2' expr:title='data:post.title'>View Details</a>
                                                            <button class='btn btn-primary' onclick='addToCart("' + data:post.id + '", "' + data:post.title + '", 99.99)'>
                                                                <i class='fas fa-cart-plus me-1'></i>Add to Cart
                                                            </button>
                                                        </div>
                                                    </div>
                                                </article>
                                            </div>
                                        </b:loop>
                                    </div>
                                </div>
                            </section>
                        </div>
                    <b:else/>
                        <!-- Blog Post Content -->
                        <b:if cond='data:view.isPost'>
                            <div class='container my-5'>
                                <div class='row'>
                                    <div class='col-lg-8 mx-auto'>
                                        <article class='blog-post'>
                                            <header class='post-header mb-4'>
                                                <h1 class='post-title'><data:post.title/></h1>
                                                <div class='post-meta'>
                                                    <i class='fas fa-calendar-alt'></i>
                                                    <span class='post-timestamp'><data:post.timestamp/></span>
                                                    <b:if cond='data:post.labels'>
                                                        <span class='ms-3'>
                                                            <i class='fas fa-tags'></i>
                                                            <b:loop values='data:post.labels' var='label'>
                                                                <a expr:href='data:label.url' class='text-decoration-none me-2'><data:label.name/></a>
                                                            </b:loop>
                                                        </span>
                                                    </b:if>
                                                </div>
                                            </header>
                                            <div class='post-body'>
                                                <data:post.body/>
                                            </div>
                                            <footer class='post-footer mt-4'>
                                                <b:if cond='data:post.allowComments'>
                                                    <b:include name='comments'/>
                                                </b:if>
                                            </footer>
                                        </article>
                                    </div>
                                </div>
                            </div>
                        <b:else/>
                            <!-- Archive/Label Pages -->
                            <div class='container my-5'>
                                <div class='row'>
                                    <div class='col-12'>
                                        <h1 class='mb-4'>
                                            <b:if cond='data:view.isLabelSearch'>
                                                Products in "<data:view.search.label/>"
                                            <b:else/>
                                                <data:view.title.escaped/>
                                            </b:if>
                                        </h1>
                                        <div class='row g-4'>
                                            <b:loop values='data:posts' var='post'>
                                                <div class='col-lg-4 col-md-6 mb-4'>
                                                    <article class='product-card h-100 bg-white border-0 shadow-sm'>
                                                        <div class='product-image'>
                                                            <b:if cond='data:post.featuredImage'>
                                                                <img expr:alt='data:post.title' expr:src='data:post.featuredImage' class='card-img-top' style='height: 200px; object-fit: cover;'/>
                                                            <b:else/>
                                                                <i class='fas fa-shopping-bag'></i>
                                                            </b:if>
                                                        </div>
                                                        <div class='card-body d-flex flex-column'>
                                                            <h5 class='card-title'><data:post.title/></h5>
                                                            <p class='card-text flex-grow-1'><data:post.snippet/></p>
                                                            <div class='d-flex justify-content-between align-items-center'>
                                                                <div class='price-info'>
                                                                    <span class='price'>$99.99</span>
                                                                </div>
                                                                <div class='rating'>
                                                                    <i class='fas fa-star'></i>
                                                                    <i class='fas fa-star'></i>
                                                                    <i class='fas fa-star'></i>
                                                                    <i class='fas fa-star'></i>
                                                                    <i class='far fa-star'></i>
                                                                    <small class='text-muted'>(4.0)</small>
                                                                </div>
                                                            </div>
                                                            <div class='mt-3'>
                                                                <a expr:href='data:post.url' class='btn btn-outline-primary me-2' expr:title='data:post.title'>View Details</a>
                                                                <button class='btn btn-primary' onclick='addToCart("' + data:post.id + '", "' + data:post.title + '", 99.99)'>
                                                                    <i class='fas fa-cart-plus me-1'></i>Add to Cart
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </article>
                                                </div>
                                            </b:loop>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </b:if>
                    </b:if>
                </b:includable>
                <b:includable id='comments'>
                    <div class='comments-section mt-5'>
                        <h3>Comments</h3>
                        <div id='comments-block-wrapper'>
                            <dl id='comments-block'>
                                <b:loop values='data:post.comments' var='comment'>
                                    <dt expr:class='&quot;comment-author &quot; + data:comment.authorClass' expr:id='data:comment.anchorName'>
                                        <b:if cond='data:comment.authorUrl'>
                                            <a expr:href='data:comment.authorUrl' rel='nofollow'><data:comment.author/></a>
                                        <b:else/>
                                            <data:comment.author/>
                                        </b:if>
                                        <data:comment.timeStamp/>
                                    </dt>
                                    <dd class='comment-body'>
                                        <b:if cond='data:comment.isDeleted'>
                                            <span class='deleted-comment'><data:comment.body/></span>
                                        <b:else/>
                                            <p><data:comment.body/></p>
                                        </b:if>
                                    </dd>
                                </b:loop>
                            </dl>
                        </div>
                        <b:if cond='data:post.embedCommentForm'>
                            <b:include data='post' name='comment-form'/>
                        </b:if>
                    </div>
                </b:includable>
                <b:includable id='comment-form'>
                    <div class='comment-form'>
                        <h4>Leave a Comment</h4>
                        <form expr:action='data:post.commentFormIframeSrc' method='post' name='comment-form'>
                            <div class='mb-3'>
                                <label for='comment-name' class='form-label'>Name</label>
                                <input class='form-control' id='comment-name' name='name' type='text' required='required'/>
                            </div>
                            <div class='mb-3'>
                                <label for='comment-email' class='form-label'>Email</label>
                                <input class='form-control' id='comment-email' name='email' type='email' required='required'/>
                            </div>
                            <div class='mb-3'>
                                <label for='comment-body' class='form-label'>Comment</label>
                                <textarea class='form-control' id='comment-body' name='body' rows='4' required='required'></textarea>
                            </div>
                            <button class='btn btn-primary' type='submit'>Post Comment</button>
                        </form>
                    </div>
                </b:includable>
            </b:widget>
        </b:section>
    </main>

    <!-- Contact Section -->
    <section id='contact' class='py-5'>
        <div class='container'>
            <div class='row'>
                <div class='col-lg-8 mx-auto'>
                    <h2 class='text-center mb-5'>Contact Us</h2>
                    <form id='contactForm' class='needs-validation' novalidate='novalidate'>
                        <div class='row'>
                            <div class='col-md-6 mb-3'>
                                <label for='firstName' class='form-label'>First Name</label>
                                <input type='text' class='form-control' id='firstName' required='required'/>
                                <div class='invalid-feedback'>Please provide your first name.</div>
                            </div>
                            <div class='col-md-6 mb-3'>
                                <label for='lastName' class='form-label'>Last Name</label>
                                <input type='text' class='form-control' id='lastName' required='required'/>
                                <div class='invalid-feedback'>Please provide your last name.</div>
                            </div>
                        </div>
                        <div class='mb-3'>
                            <label for='email' class='form-label'>Email</label>
                            <input type='email' class='form-control' id='email' required='required'/>
                            <div class='invalid-feedback'>Please provide a valid email.</div>
                        </div>
                        <div class='mb-3'>
                            <label for='subject' class='form-label'>Subject</label>
                            <input type='text' class='form-control' id='subject' required='required'/>
                            <div class='invalid-feedback'>Please provide a subject.</div>
                        </div>
                        <div class='mb-3'>
                            <label for='message' class='form-label'>Message</label>
                            <textarea class='form-control' id='message' rows='5' required='required'></textarea>
                            <div class='invalid-feedback'>Please provide a message.</div>
                        </div>
                        <div class='text-center'>
                            <button type='submit' class='btn btn-primary btn-lg'>
                                <i class='fas fa-paper-plane me-2'></i>Send Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class='bg-dark text-light py-5'>
        <div class='container'>
            <div class='row'>
                <div class='col-lg-4 mb-4'>
                    <h5><i class='fas fa-store me-2'></i><data:blog.title/></h5>
                    <p>Your trusted online shopping destination with quality products and excellent customer service.</p>
                    <div class='social-links'>
                        <a href='#' class='text-light me-3' title='Facebook'><i class='fab fa-facebook-f'></i></a>
                        <a href='#' class='text-light me-3' title='Twitter'><i class='fab fa-twitter'></i></a>
                        <a href='#' class='text-light me-3' title='Instagram'><i class='fab fa-instagram'></i></a>
                        <a href='#' class='text-light me-3' title='LinkedIn'><i class='fab fa-linkedin-in'></i></a>
                    </div>
                </div>
                <div class='col-lg-2 mb-4'>
                    <h6>Quick Links</h6>
                    <ul class='list-unstyled'>
                        <li><a href='#' class='text-light text-decoration-none'>About Us</a></li>
                        <li><a href='#' class='text-light text-decoration-none'>Privacy Policy</a></li>
                        <li><a href='#' class='text-light text-decoration-none'>Terms of Service</a></li>
                        <li><a href='#' class='text-light text-decoration-none'>Shipping Info</a></li>
                    </ul>
                </div>
                <div class='col-lg-2 mb-4'>
                    <h6>Categories</h6>
                    <ul class='list-unstyled'>
                        <li><a href='/search/label/Electronics' class='text-light text-decoration-none'>Electronics</a></li>
                        <li><a href='/search/label/Clothing' class='text-light text-decoration-none'>Clothing</a></li>
                        <li><a href='/search/label/Books' class='text-light text-decoration-none'>Books</a></li>
                        <li><a href='/search/label/Home' class='text-light text-decoration-none'>Home &amp; Garden</a></li>
                    </ul>
                </div>
                <div class='col-lg-4 mb-4'>
                    <h6>Contact Info</h6>
                    <ul class='list-unstyled'>
                        <li><i class='fas fa-map-marker-alt me-2'></i>123 E-Commerce Street, City, Country</li>
                        <li><i class='fas fa-phone me-2'></i>+****************</li>
                        <li><i class='fas fa-envelope me-2'></i><EMAIL></li>
                    </ul>
                </div>
            </div>
            <hr class='my-4'/>
            <div class='row align-items-center'>
                <div class='col-md-6'>
                    <p class='mb-0'>&copy; 2025 <data:blog.title/>. All rights reserved.</p>
                </div>
                <div class='col-md-6 text-md-end'>
                    <p class='mb-0'>
                        <i class='fas fa-credit-card me-2'></i>
                        <i class='fab fa-cc-visa me-2'></i>
                        <i class='fab fa-cc-mastercard me-2'></i>
                        <i class='fab fa-paypal me-2'></i>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Shopping Cart Modal -->
    <div class='modal fade' id='cartModal' tabindex='-1' aria-labelledby='cartModalLabel' aria-hidden='true'>
        <div class='modal-dialog modal-lg'>
            <div class='modal-content'>
                <div class='modal-header'>
                    <h5 class='modal-title' id='cartModalLabel'>Shopping Cart</h5>
                    <button type='button' class='btn-close' data-bs-dismiss='modal' aria-label='Close'></button>
                </div>
                <div class='modal-body'>
                    <div id='cartItems'>
                        <!-- Cart items will be loaded here -->
                    </div>
                    <div class='cart-empty text-center py-5' id='cartEmpty'>
                        <i class='fas fa-shopping-cart display-4 text-muted mb-3'></i>
                        <h5>Your cart is empty</h5>
                        <p class='text-muted'>Add some products to get started!</p>
                    </div>
                </div>
                <div class='modal-footer'>
                    <div class='w-100'>
                        <div class='d-flex justify-content-between align-items-center mb-3'>
                            <h5>Total: $<span id='cartTotal'>0.00</span></h5>
                        </div>
                        <button type='button' class='btn btn-secondary' data-bs-dismiss='modal'>Continue Shopping</button>
                        <button type='button' class='btn btn-primary' id='checkoutBtn'>
                            <i class='fas fa-credit-card me-2'></i>Proceed to Checkout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
    
    <!-- Custom JavaScript -->
    <script>
        //<![CDATA[
        // E-Commerce functionality
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        let products = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            updateCartUI();
            initializeEventListeners();
            loadProducts();
        });

        // Initialize event listeners
        function initializeEventListeners() {
            // Cart button
            document.getElementById('cartBtn').addEventListener('click', function() {
                showCart();
            });

            // Search functionality
            document.getElementById('searchBtn').addEventListener('click', performSearch);
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Contact form
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', handleContactForm);
            }

            // Checkout button
            document.addEventListener('click', function(e) {
                if (e.target.id === 'checkoutBtn') {
                    handleCheckout();
                }
            });
        }

        // Add item to cart
        function addToCart(id, name, price) {
            const existingItem = cart.find(item => item.id === id);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: id,
                    name: name,
                    price: parseFloat(price),
                    quantity: 1
                });
            }
            
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartUI();
            
            // Show success message
            showToast('Product added to cart!', 'success');
        }

        // Remove item from cart
        function removeFromCart(id) {
            cart = cart.filter(item => item.id !== id);
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartUI();
            showCart(); // Refresh cart modal
        }

        // Update cart UI
        function updateCartUI() {
            const cartCount = document.getElementById('cartCount');
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        // Show cart modal
        function showCart() {
            const cartModal = new bootstrap.Modal(document.getElementById('cartModal'));
            const cartItems = document.getElementById('cartItems');
            const cartEmpty = document.getElementById('cartEmpty');
            const cartTotal = document.getElementById('cartTotal');
            
            if (cart.length === 0) {
                cartItems.style.display = 'none';
                cartEmpty.style.display = 'block';
                cartTotal.textContent = '0.00';
            } else {
                cartItems.style.display = 'block';
                cartEmpty.style.display = 'none';
                
                let cartHTML = '';
                let total = 0;
                
                cart.forEach(item => {
                    total += item.price * item.quantity;
                    cartHTML += `
                        <div class="cart-item d-flex justify-content-between align-items-center">
                            <div class="item-info">
                                <h6>${item.name}</h6>
                                <p class="text-muted">$${item.price.toFixed(2)} each</p>
                            </div>
                            <div class="quantity-controls">
                                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">-</button>
                                <span class="mx-2">${item.quantity}</span>
                                <button class="btn btn-sm btn-outline-secondary" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">+</button>
                                <button class="btn btn-sm btn-outline-danger ms-2" onclick="removeFromCart('${item.id}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                cartItems.innerHTML = cartHTML;
                cartTotal.textContent = total.toFixed(2);
            }
            
            cartModal.show();
        }

        // Update item quantity
        function updateQuantity(id, newQuantity) {
            if (newQuantity <= 0) {
                removeFromCart(id);
                return;
            }
            
            const item = cart.find(item => item.id === id);
            if (item) {
                item.quantity = newQuantity;
                localStorage.setItem('cart', JSON.stringify(cart));
                updateCartUI();
                showCart(); // Refresh cart modal
            }
        }

        // Perform search
        function performSearch() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            if (searchTerm) {
                // In a real Blogger implementation, this would redirect to search results
                window.location.href = `/search?q=${encodeURIComponent(searchTerm)}`;
            }
        }

        // Handle contact form submission
        function handleContactForm(e) {
            e.preventDefault();
            
            // Validate form
            const form = e.target;
            if (!form.checkValidity()) {
                form.classList.add('was-validated');
                return;
            }
            
            // Simulate form submission
            showToast('Message sent successfully! We\'ll get back to you soon.', 'success');
            form.reset();
            form.classList.remove('was-validated');
        }

        // Handle checkout
        function handleCheckout() {
            if (cart.length === 0) {
                showToast('Your cart is empty!', 'warning');
                return;
            }
            
            // Simulate checkout process
            showToast('Redirecting to checkout...', 'info');
            
            // In a real implementation, this would redirect to a payment processor
            setTimeout(() => {
                showToast('Thank you for your order! (This is a demo)', 'success');
                cart = [];
                localStorage.setItem('cart', JSON.stringify(cart));
                updateCartUI();
                
                // Close cart modal
                const cartModal = bootstrap.Modal.getInstance(document.getElementById('cartModal'));
                cartModal.hide();
            }, 2000);
        }

        // Show toast notification
        function showToast(message, type = 'info') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : type === 'error' ? 'danger' : 'primary'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            // Add to page
            let toastContainer = document.getElementById('toastContainer');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toastContainer';
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                document.body.appendChild(toastContainer);
            }
            
            toastContainer.appendChild(toast);
            
            // Show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }

        // Scroll to products section
        function scrollToProducts() {
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // Load products (in a real Blogger setup, this would be handled by the template)
        function loadProducts() {
            // This function would typically fetch products from Blogger posts
            // For now, it's handled by the Blogger template itself
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        //]]>
    </script>
</body>
</html>
