/**
 * Orders Management System
 */

/**
 * Show user orders
 */
function showOrders() {
    if (!currentUser) {
        showLoginModal();
        return;
    }
    
    const modal = document.getElementById('ordersModal');
    const modalBody = document.getElementById('ordersModalBody');
    
    // Get user orders
    const userOrders = orders.filter(order => order.userId === currentUser.id);
    
    if (userOrders.length === 0) {
        modalBody.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-shopping-bag display-4 text-muted mb-3"></i>
                <h5>لا توجد طلبات</h5>
                <p class="text-muted">لم تقم بأي طلبات حتى الآن</p>
                <button class="btn btn-primary" onclick="scrollToProducts(); bootstrap.Modal.getInstance(document.getElementById('ordersModal')).hide();">
                    <i class="fas fa-shopping-cart ms-2"></i>ابدأ التسوق
                </button>
            </div>
        `;
    } else {
        modalBody.innerHTML = `
            <div class="orders-list">
                ${userOrders.map(order => createOrderCard(order)).join('')}
            </div>
        `;
    }
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Create order card HTML
 */
function createOrderCard(order) {
    const statusColors = {
        'pending': 'warning',
        'processing': 'info',
        'shipped': 'primary',
        'delivered': 'success',
        'cancelled': 'danger'
    };
    
    const statusTexts = {
        'pending': 'في الانتظار',
        'processing': 'قيد المعالجة',
        'shipped': 'تم الشحن',
        'delivered': 'تم التسليم',
        'cancelled': 'ملغي'
    };
    
    return `
        <div class="card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-0">طلب رقم: ${order.id}</h6>
                    <small class="text-muted">${new Date(order.date).toLocaleDateString('ar-SA')}</small>
                </div>
                <span class="badge bg-${statusColors[order.status]} fs-6">
                    ${statusTexts[order.status]}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>المنتجات:</h6>
                        <div class="order-items">
                            ${order.items.map(item => `
                                <div class="d-flex align-items-center mb-2">
                                    <img src="${item.image || 'https://via.placeholder.com/50'}" 
                                         alt="${item.name}" 
                                         class="rounded me-3" 
                                         style="width: 50px; height: 50px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">${item.name}</div>
                                        <small class="text-muted">الكمية: ${item.quantity} × $${item.price.toFixed(2)}</small>
                                    </div>
                                    <div class="text-end">
                                        <strong>$${(item.quantity * item.price).toFixed(2)}</strong>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="order-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المجموع الفرعي:</span>
                                <span>$${order.subtotal.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الشحن:</span>
                                <span>$${order.shipping.toFixed(2)}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>الضريبة:</span>
                                <span>$${order.tax.toFixed(2)}</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between fw-bold">
                                <span>الإجمالي:</span>
                                <span class="text-success">$${order.total.toFixed(2)}</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="trackOrder('${order.id}')">
                                <i class="fas fa-truck ms-1"></i>تتبع الطلب
                            </button>
                            ${order.status === 'pending' ? `
                                <button class="btn btn-outline-danger btn-sm w-100" onclick="cancelOrder('${order.id}')">
                                    <i class="fas fa-times ms-1"></i>إلغاء الطلب
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Create new order from cart
 */
function createOrder(cartItems, shippingInfo, paymentInfo) {
    if (!currentUser) {
        showLoginModal();
        return null;
    }
    
    const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const shipping = subtotal > 100 ? 0 : 10; // Free shipping over $100
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + shipping + tax;
    
    const order = {
        id: generateOrderId(),
        userId: currentUser.id,
        date: new Date().toISOString(),
        status: 'pending',
        items: cartItems.map(item => ({
            productId: item.id,
            name: item.name,
            price: item.price,
            quantity: item.quantity,
            image: item.image
        })),
        subtotal,
        shipping,
        tax,
        total,
        shippingInfo,
        paymentInfo,
        trackingNumber: generateTrackingNumber()
    };
    
    // Add to orders array
    orders.push(order);
    localStorage.setItem('orders', JSON.stringify(orders));
    
    // Add to user's orders
    currentUser.orders.push(order.id);
    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex !== -1) {
        users[userIndex] = currentUser;
        localStorage.setItem('users', JSON.stringify(users));
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
    }
    
    return order;
}

/**
 * Track order
 */
function trackOrder(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;
    
    const trackingSteps = [
        { status: 'pending', text: 'تم استلام الطلب', completed: true },
        { status: 'processing', text: 'قيد المعالجة', completed: ['processing', 'shipped', 'delivered'].includes(order.status) },
        { status: 'shipped', text: 'تم الشحن', completed: ['shipped', 'delivered'].includes(order.status) },
        { status: 'delivered', text: 'تم التسليم', completed: order.status === 'delivered' }
    ];
    
    const trackingHTML = `
        <div class="tracking-info">
            <h6 class="mb-3">تتبع الطلب: ${orderId}</h6>
            <p class="mb-3">رقم التتبع: <strong>${order.trackingNumber}</strong></p>
            
            <div class="tracking-steps">
                ${trackingSteps.map((step, index) => `
                    <div class="tracking-step ${step.completed ? 'completed' : ''}">
                        <div class="step-icon">
                            <i class="fas fa-${step.completed ? 'check' : 'circle'}"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">${step.text}</div>
                            ${step.completed ? '<small class="text-success">مكتمل</small>' : '<small class="text-muted">في الانتظار</small>'}
                        </div>
                    </div>
                `).join('')}
            </div>
            
            <div class="mt-3 p-3 bg-light rounded">
                <h6>معلومات الشحن:</h6>
                <p class="mb-1"><strong>العنوان:</strong> ${order.shippingInfo?.address || 'غير محدد'}</p>
                <p class="mb-1"><strong>المدينة:</strong> ${order.shippingInfo?.city || 'غير محدد'}</p>
                <p class="mb-0"><strong>الهاتف:</strong> ${order.shippingInfo?.phone || currentUser.phone}</p>
            </div>
        </div>
    `;
    
    // Show in a modal or alert
    showToast('معلومات التتبع متاحة في قسم الطلبات', 'info');
}

/**
 * Cancel order
 */
function cancelOrder(orderId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الطلب؟')) {
        const orderIndex = orders.findIndex(o => o.id === orderId);
        if (orderIndex !== -1) {
            orders[orderIndex].status = 'cancelled';
            localStorage.setItem('orders', JSON.stringify(orders));
            
            showToast('تم إلغاء الطلب بنجاح', 'success');
            
            // Refresh orders display
            showOrders();
        }
    }
}

/**
 * Generate order ID
 */
function generateOrderId() {
    return 'ORD-' + Date.now().toString().slice(-8) + Math.random().toString(36).substr(2, 4).toUpperCase();
}

/**
 * Generate tracking number
 */
function generateTrackingNumber() {
    return 'TRK' + Date.now().toString().slice(-10) + Math.random().toString(36).substr(2, 6).toUpperCase();
}

/**
 * Show wishlist
 */
function showWishlist() {
    if (!currentUser) {
        showToast('يجب تسجيل الدخول لعرض المفضلة', 'warning');
        if (typeof showLoginModal === 'function') {
            showLoginModal();
        }
        return;
    }

    console.log('Current wishlist:', wishlist);
    console.log('Available products:', products.length);

    const wishlistProducts = products.filter(product => wishlist.includes(product.id));
    console.log('Wishlist products found:', wishlistProducts);

    let wishlistHTML = '';
    if (wishlistProducts.length === 0) {
        wishlistHTML = `
            <div class="text-center py-5">
                <i class="fas fa-heart display-4 text-muted mb-3"></i>
                <h4>قائمة المفضلة فارغة</h4>
                <p class="text-muted">لم تقم بإضافة أي منتجات للمفضلة حتى الآن</p>
                <button class="btn btn-primary" onclick="closeWishlistModal()">
                    <i class="fas fa-shopping-cart ms-1"></i>ابدأ التسوق
                </button>
            </div>
        `;
    } else {
        wishlistHTML = `
            <div class="row">
                ${wishlistProducts.map(product => `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="wishlist-item">
                            <div class="card h-100">
                                <div class="position-relative">
                                    <img src="${product.image || 'https://via.placeholder.com/300x200'}"
                                         class="card-img-top" alt="${product.name}" style="height: 200px; object-fit: cover;">
                                    <button class="btn btn-sm btn-danger position-absolute top-0 end-0 m-2"
                                            onclick="removeFromWishlistAndRefresh('${product.id}')"
                                            title="إزالة من المفضلة">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">${product.name}</h6>
                                    <p class="card-text text-muted small">${product.description ? product.description.substring(0, 100) + '...' : ''}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="h6 text-primary mb-0">$${product.price}</span>
                                        <button class="btn btn-sm btn-primary" onclick="addToCart('${product.id}', '${product.name}', ${product.price}, '${product.image || ''}')">
                                            <i class="fas fa-cart-plus ms-1"></i>أضف للسلة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // Create wishlist modal
    const wishlistModal = document.createElement('div');
    wishlistModal.className = 'modal fade';
    wishlistModal.id = 'wishlistModal';
    wishlistModal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-heart ms-2"></i>المفضلة (${wishlistProducts.length})
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${wishlistHTML}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    ${wishlistProducts.length > 0 ? `
                        <button type="button" class="btn btn-danger" onclick="clearWishlist()">
                            <i class="fas fa-trash ms-1"></i>مسح الكل
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(wishlistModal);

    const modal = new bootstrap.Modal(wishlistModal);
    modal.show();

    // Remove modal after hiding
    wishlistModal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(wishlistModal);
    });
}

/**
 * Remove from wishlist and refresh modal
 */
function removeFromWishlistAndRefresh(productId) {
    if (typeof removeFromWishlist === 'function') {
        removeFromWishlist(productId);

        // Close current modal and reopen
        const modal = document.getElementById('wishlistModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
                setTimeout(() => {
                    showWishlist();
                }, 300);
            }
        }
    }
}

/**
 * Clear entire wishlist
 */
function clearWishlist() {
    if (confirm('هل أنت متأكد من مسح جميع المنتجات من المفضلة؟')) {
        wishlist = [];
        if (typeof saveUserPreferences === 'function') {
            saveUserPreferences();
        } else {
            localStorage.setItem('wishlist', JSON.stringify(wishlist));
        }

        showToast('تم مسح جميع المنتجات من المفضلة', 'info');

        // Close modal and refresh
        const modal = document.getElementById('wishlistModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }

        // Update wishlist UI
        if (typeof updateWishlistUI === 'function') {
            updateWishlistUI();
        }
    }
}

/**
 * Close wishlist modal
 */
function closeWishlistModal() {
    const modal = document.getElementById('wishlistModal');
    if (modal) {
        const bsModal = bootstrap.Modal.getInstance(modal);
        if (bsModal) {
            bsModal.hide();
        }
    }
}

/**
 * Add test products to wishlist for debugging
 */
function addTestWishlistItems() {
    if (!currentUser) {
        showToast('يجب تسجيل الدخول أولاً', 'warning');
        return;
    }

    // Add some test product IDs to wishlist
    const testProductIds = ['sw001', 'sw002', 'sw003'];

    testProductIds.forEach(productId => {
        if (!wishlist.includes(productId)) {
            wishlist.push(productId);
        }
    });

    // Save to localStorage
    localStorage.setItem('wishlist', JSON.stringify(wishlist));

    showToast(`تم إضافة ${testProductIds.length} منتج تجريبي للمفضلة`, 'success');

    // Update UI if function exists
    if (typeof updateWishlistUI === 'function') {
        updateWishlistUI();
    }
}

// Add test function to console for debugging
if (typeof window !== 'undefined') {
    window.addTestWishlistItems = addTestWishlistItems;
    console.log('Test function available: addTestWishlistItems()');
}
