/**
 * Authentication and User Management System
 */

// Global user state
let currentUser = null;
let users = JSON.parse(localStorage.getItem('users') || '[]');
let orders = JSON.parse(localStorage.getItem('orders') || '[]');

/**
 * Initialize authentication system
 */
function initializeAuth() {
    // Check if user is logged in
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUserUI();
    }
    
    // Setup form event listeners
    setupAuthEventListeners();
}

/**
 * Setup event listeners for auth forms
 */
function setupAuthEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
}

/**
 * Handle login form submission
 */
function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Find user
    const user = users.find(u => u.email === email && u.password === password);
    
    if (user) {
        currentUser = user;
        
        // Save to localStorage
        if (rememberMe) {
            localStorage.setItem('currentUser', JSON.stringify(user));
        } else {
            sessionStorage.setItem('currentUser', JSON.stringify(user));
        }
        
        // Update UI
        updateUserUI();
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        modal.hide();
        
        // Show success message
        showToast('تم تسجيل الدخول بنجاح!', 'success');
        
        // Clear form
        document.getElementById('loginForm').reset();
        
    } else {
        showToast('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'danger');
    }
}

/**
 * Handle register form submission
 */
function handleRegister(e) {
    e.preventDefault();
    
    const firstName = document.getElementById('firstName').value;
    const lastName = document.getElementById('lastName').value;
    const email = document.getElementById('registerEmail').value;
    const phone = document.getElementById('phone').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validation
    if (password !== confirmPassword) {
        showToast('كلمات المرور غير متطابقة', 'danger');
        return;
    }
    
    // Check if email already exists
    if (users.find(u => u.email === email)) {
        showToast('البريد الإلكتروني مستخدم بالفعل', 'danger');
        return;
    }
    
    // Create new user
    const newUser = {
        id: generateUserId(),
        firstName,
        lastName,
        email,
        phone,
        password,
        registrationDate: new Date().toISOString(),
        orders: [],
        wishlist: [],
        addresses: []
    };
    
    // Add to users array
    users.push(newUser);
    localStorage.setItem('users', JSON.stringify(users));
    
    // Auto login
    currentUser = newUser;
    localStorage.setItem('currentUser', JSON.stringify(newUser));
    
    // Update UI
    updateUserUI();
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
    modal.hide();
    
    // Show success message
    showToast('تم إنشاء الحساب بنجاح!', 'success');
    
    // Clear form
    document.getElementById('registerForm').reset();
}

/**
 * Update user interface based on login state
 */
function updateUserUI() {
    const loggedOutState = document.getElementById('loggedOutState');
    const loggedInState = document.getElementById('loggedInState');
    const userName = document.getElementById('userName');
    
    if (currentUser) {
        // Show logged in state
        loggedOutState.classList.add('d-none');
        loggedInState.classList.remove('d-none');
        userName.textContent = currentUser.firstName;
    } else {
        // Show logged out state
        loggedOutState.classList.remove('d-none');
        loggedInState.classList.add('d-none');
    }
}

/**
 * Show login modal
 */
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
    
    // Hide register modal if open
    const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
    if (registerModal) {
        registerModal.hide();
    }
}

/**
 * Show register modal
 */
function showRegisterModal() {
    const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
    registerModal.show();
    
    // Hide login modal if open
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
}

/**
 * Logout user
 */
function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
    
    // Update UI
    updateUserUI();
    
    // Show success message
    showToast('تم تسجيل الخروج بنجاح', 'info');
}

/**
 * Show user profile
 */
function showProfile() {
    if (!currentUser) {
        showLoginModal();
        return;
    }
    
    const modal = document.getElementById('profileModal');
    const modalBody = document.getElementById('profileModalBody');
    
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-4 text-center">
                <div class="profile-avatar mb-3">
                    <i class="fas fa-user-circle display-1 text-primary"></i>
                </div>
                <h5>${currentUser.firstName} ${currentUser.lastName}</h5>
                <p class="text-muted">${currentUser.email}</p>
            </div>
            <div class="col-md-8">
                <form id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" value="${currentUser.firstName}" id="profileFirstName">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العائلة</label>
                            <input type="text" class="form-control" value="${currentUser.lastName}" id="profileLastName">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" value="${currentUser.email}" id="profileEmail" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" value="${currentUser.phone}" id="profilePhone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ التسجيل</label>
                        <input type="text" class="form-control" value="${new Date(currentUser.registrationDate).toLocaleDateString('ar-SA')}" readonly>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="updateProfile()">
                        <i class="fas fa-save ms-2"></i>حفظ التغييرات
                    </button>
                </form>
            </div>
        </div>
    `;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Update user profile
 */
function updateProfile() {
    const firstName = document.getElementById('profileFirstName').value;
    const lastName = document.getElementById('profileLastName').value;
    const phone = document.getElementById('profilePhone').value;
    
    // Update current user
    currentUser.firstName = firstName;
    currentUser.lastName = lastName;
    currentUser.phone = phone;
    
    // Update in users array
    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex !== -1) {
        users[userIndex] = currentUser;
        localStorage.setItem('users', JSON.stringify(users));
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
    }
    
    // Update UI
    updateUserUI();
    
    showToast('تم تحديث الملف الشخصي بنجاح', 'success');
}

/**
 * Generate unique user ID
 */
function generateUserId() {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Show terms and conditions
 */
function showTerms() {
    showToast('الشروط والأحكام ستكون متاحة قريباً', 'info');
}

// Initialize auth system when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeAuth);
