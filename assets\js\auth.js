/**
 * Authentication and User Management System
 * Uses SecureDatabase for encrypted data storage
 */

// Global user state
let currentUser = null;

/**
 * Initialize authentication system
 */
function initializeAuth() {
    // Check if user is logged in
    const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    if (savedUser) {
        try {
            currentUser = JSON.parse(savedUser);
            // Verify user still exists in database
            const dbUser = secureDB.getUserById(currentUser.id);
            if (dbUser) {
                currentUser = dbUser;
                updateUserUI();
            } else {
                // User no longer exists, clear session
                logout();
            }
        } catch (e) {
            console.error('Error loading user session:', e);
            logout();
        }
    }

    // Setup form event listeners
    setupAuthEventListeners();
}

/**
 * Setup event listeners for auth forms
 */
function setupAuthEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
}

/**
 * Handle login form submission
 */
function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Authenticate user using secure database
    const authResult = secureDB.authenticateUser(email, password);

    if (authResult.success) {
        currentUser = authResult.user;

        // Save to localStorage or sessionStorage
        if (rememberMe) {
            localStorage.setItem('currentUser', JSON.stringify(authResult.user));
        } else {
            sessionStorage.setItem('currentUser', JSON.stringify(authResult.user));
        }

        // Update UI
        updateUserUI();

        // Refresh products to show prices
        if (typeof displayProducts === 'function') {
            displayProducts();
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
        modal.hide();

        // Show success message
        showToast('تم تسجيل الدخول بنجاح! يمكنك الآن رؤية الأسعار والتفاصيل', 'success');

        // Clear form
        document.getElementById('loginForm').reset();

    } else {
        showToast(authResult.error || 'البريد الإلكتروني أو كلمة المرور غير صحيحة', 'danger');
    }
}

/**
 * Handle register form submission
 */
function handleRegister(e) {
    e.preventDefault();

    const firstName = document.getElementById('firstName').value.trim();
    const lastName = document.getElementById('lastName').value.trim();
    const email = document.getElementById('registerEmail').value.trim().toLowerCase();
    const phone = document.getElementById('phone').value.trim();
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Enhanced validation
    if (!validateRegistrationData(firstName, lastName, email, phone, password, confirmPassword)) {
        return;
    }

    // Check if email already exists
    if (secureDB.emailExists(email)) {
        showToast('البريد الإلكتروني مستخدم بالفعل', 'danger');
        return;
    }

    // Get selected avatar and country
    const selectedAvatar = document.getElementById('selectedAvatar').value || 'user';
    const countrySelect = document.getElementById('country');
    const selectedCountry = countrySelect ? countrySelect.value : 'SA';
    const countryName = countrySelect ? countrySelect.options[countrySelect.selectedIndex].textContent.split('(')[0].trim() : 'السعودية';
    const countryCode = countrySelect ? countrySelect.options[countrySelect.selectedIndex].getAttribute('data-code') : '+966';

    // Create user data
    const userData = {
        firstName,
        lastName,
        email,
        phone,
        country: selectedCountry,
        countryName: countryName,
        countryCode: countryCode,
        password,
        avatar: selectedAvatar
    };

    // Create user using secure database
    const createResult = secureDB.createUser(userData);

    if (!createResult.success) {
        showToast(createResult.error || 'حدث خطأ أثناء إنشاء الحساب', 'danger');
        return;
    }
    
    // Auto login with the created user
    currentUser = createResult.user;
    localStorage.setItem('currentUser', JSON.stringify(createResult.user));

    // Update UI
    updateUserUI();

    // Refresh products to show prices
    if (typeof displayProducts === 'function') {
        displayProducts();
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
    modal.hide();

    // Show success message
    showToast('تم إنشاء الحساب بنجاح! يمكنك الآن رؤية الأسعار والتفاصيل', 'success');
    
    // Clear form
    document.getElementById('registerForm').reset();
}

/**
 * Update user interface based on login state
 */
function updateUserUI() {
    const loggedOutState = document.getElementById('loggedOutState');
    const loggedInState = document.getElementById('loggedInState');
    const userName = document.getElementById('userName');
    
    if (currentUser) {
        // Show logged in state
        loggedOutState.classList.add('d-none');
        loggedInState.classList.remove('d-none');

        // Update user name with avatar
        const userAvatar = currentUser.avatar || 'user';
        userName.innerHTML = `<i class="fas fa-${userAvatar} ms-1"></i>${currentUser.firstName}`;

        // Update dropdown button
        const dropdownButton = document.querySelector('.logged-in-state .dropdown-toggle');
        if (dropdownButton) {
            dropdownButton.innerHTML = `<i class="fas fa-${userAvatar} ms-1"></i><span class="ms-1">${currentUser.firstName}</span>`;
        }
    } else {
        // Show logged out state
        loggedOutState.classList.remove('d-none');
        loggedInState.classList.add('d-none');
    }
}

/**
 * Show login modal
 */
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
    
    // Hide register modal if open
    const registerModal = bootstrap.Modal.getInstance(document.getElementById('registerModal'));
    if (registerModal) {
        registerModal.hide();
    }
}

/**
 * Show register modal
 */
function showRegisterModal() {
    const registerModal = new bootstrap.Modal(document.getElementById('registerModal'));
    registerModal.show();
    
    // Hide login modal if open
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
}

/**
 * Logout user
 */
function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');

    // Update UI
    updateUserUI();

    // Refresh products to hide prices
    if (typeof displayProducts === 'function') {
        displayProducts();
    }

    // Show success message
    showToast('تم تسجيل الخروج بنجاح', 'info');
}

/**
 * Show user profile
 */
function showProfile() {
    if (!currentUser) {
        showLoginModal();
        return;
    }
    
    const modal = document.getElementById('profileModal');
    const modalBody = document.getElementById('profileModalBody');
    
    const userAvatar = currentUser.avatar || 'user';

    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-4 text-center">
                <div class="profile-avatar mb-3">
                    <div class="user-avatar-large">
                        <i class="fas fa-${userAvatar}"></i>
                    </div>
                </div>
                <h5>${currentUser.firstName} ${currentUser.lastName}</h5>
                <p class="text-muted">${currentUser.email}</p>
            </div>
            <div class="col-md-8">
                <form id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" value="${currentUser.firstName}" id="profileFirstName">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم العائلة</label>
                            <input type="text" class="form-control" value="${currentUser.lastName}" id="profileLastName">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" value="${currentUser.email}" id="profileEmail" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" value="${currentUser.phone}" id="profilePhone">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تاريخ التسجيل</label>
                        <input type="text" class="form-control" value="${new Date(currentUser.registrationDate).toLocaleDateString('ar-SA')}" readonly>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="updateProfile()">
                        <i class="fas fa-save ms-2"></i>حفظ التغييرات
                    </button>
                </form>
            </div>
        </div>
    `;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

/**
 * Update user profile
 */
function updateProfile() {
    const firstName = document.getElementById('profileFirstName').value;
    const lastName = document.getElementById('profileLastName').value;
    const phone = document.getElementById('profilePhone').value;
    
    // Update current user
    currentUser.firstName = firstName;
    currentUser.lastName = lastName;
    currentUser.phone = phone;
    
    // Update in users array
    const userIndex = users.findIndex(u => u.id === currentUser.id);
    if (userIndex !== -1) {
        users[userIndex] = currentUser;
        localStorage.setItem('users', JSON.stringify(users));
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
    }
    
    // Update UI
    updateUserUI();
    
    showToast('تم تحديث الملف الشخصي بنجاح', 'success');
}

// User ID generation is now handled by SecureDatabase

/**
 * Validate registration data
 */
function validateRegistrationData(firstName, lastName, email, phone, password, confirmPassword) {
    // Name validation
    if (firstName.length < 2 || lastName.length < 2) {
        showToast('الاسم يجب أن يكون أكثر من حرفين', 'danger');
        return false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showToast('البريد الإلكتروني غير صحيح', 'danger');
        return false;
    }

    // Phone validation based on selected country
    if (!validatePhoneByCountry(phone)) {
        return false;
    }

    // Password validation
    if (password.length < 8) {
        showToast('كلمة المرور يجب أن تكون 8 أحرف على الأقل', 'danger');
        return false;
    }

    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
        showToast('كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم', 'danger');
        return false;
    }

    // Password confirmation
    if (password !== confirmPassword) {
        showToast('كلمات المرور غير متطابقة', 'danger');
        return false;
    }

    return true;
}

// Password hashing is now handled by SecureDatabase with salt

/**
 * Sanitize user input
 */
function sanitizeInput(input) {
    return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/[<>]/g, '');
}

/**
 * Show terms and conditions
 */
function showTerms() {
    showToast('الشروط والأحكام ستكون متاحة قريباً', 'info');
}

/**
 * Check password strength
 */
function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        /.{8,}/, // At least 8 characters
        /[a-z]/, // Lowercase letter
        /[A-Z]/, // Uppercase letter
        /[0-9]/, // Number
        /[^A-Za-z0-9]/ // Special character
    ];

    checks.forEach(check => {
        if (check.test(password)) strength++;
    });

    return {
        score: strength,
        text: ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'][strength] || 'ضعيف جداً'
    };
}

/**
 * Initialize country selection and phone validation
 */
function initializeCountrySelection() {
    const countrySelect = document.getElementById('country');
    const countryCodeSpan = document.getElementById('countryCode');
    const phoneInput = document.getElementById('phone');
    const phoneHelp = document.getElementById('phoneHelp');

    if (!countrySelect || !countryCodeSpan || !phoneInput) return;

    // Set default country (Saudi Arabia)
    countrySelect.value = 'SA';
    updatePhoneFormat();

    countrySelect.addEventListener('change', updatePhoneFormat);

    // Add real-time phone validation
    phoneInput.addEventListener('input', function() {
        validatePhoneRealTime();
    });

    function updatePhoneFormat() {
        const selectedOption = countrySelect.options[countrySelect.selectedIndex];

        if (selectedOption && selectedOption.value) {
            const countryCode = selectedOption.getAttribute('data-code') || '+966';
            const placeholder = selectedOption.getAttribute('data-placeholder') || 'xxxxxxxx';

            countryCodeSpan.textContent = countryCode;
            phoneInput.placeholder = placeholder;
            phoneInput.value = ''; // Clear previous input

            // Update help text
            if (phoneHelp) {
                phoneHelp.innerHTML = `
                    <i class="fas fa-info-circle"></i>
                    أدخل رقم الهاتف بدون رمز الدولة (${countryCode})
                `;
            }

            // Remove validation classes
            phoneInput.classList.remove('is-valid', 'is-invalid');
        }
    }

    function validatePhoneRealTime() {
        const phone = phoneInput.value.trim();
        if (phone.length === 0) {
            phoneInput.classList.remove('is-valid', 'is-invalid');
            return;
        }

        if (validatePhoneByCountry(phone, true)) { // true for silent validation
            phoneInput.classList.remove('is-invalid');
            phoneInput.classList.add('is-valid');
        } else {
            phoneInput.classList.remove('is-valid');
            phoneInput.classList.add('is-invalid');
        }
    }
}

/**
 * Validate phone number based on selected country
 */
function validatePhoneByCountry(phone, silent = false) {
    const countrySelect = document.getElementById('country');
    if (!countrySelect) return false;

    const selectedOption = countrySelect.options[countrySelect.selectedIndex];
    if (!selectedOption || !selectedOption.value) {
        if (!silent) {
            showToast('يرجى اختيار دولة الإقامة أولاً', 'warning');
        }
        return false;
    }

    const pattern = selectedOption.getAttribute('data-pattern');
    const countryName = selectedOption.textContent.split('(')[0].trim();

    if (!pattern) return true; // If no pattern specified, accept any format

    // Clean phone number
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '');

    // Create regex from pattern
    const phoneRegex = new RegExp(pattern);

    if (!phoneRegex.test(cleanPhone)) {
        if (!silent) {
            const placeholder = selectedOption.getAttribute('data-placeholder') || 'xxxxxxxx';
            showToast(`رقم الهاتف غير صحيح لدولة ${countryName}. التنسيق المطلوب: ${placeholder}`, 'danger');
        }
        return false;
    }

    return true;
}

/**
 * Initialize avatar selection
 */
function initializeAvatarSelection() {
    const avatarOptions = document.querySelectorAll('.avatar-option');
    const selectedAvatarInput = document.getElementById('selectedAvatar');

    // Set default selection
    if (avatarOptions.length > 0) {
        avatarOptions[0].classList.add('selected');
    }

    avatarOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            avatarOptions.forEach(opt => opt.classList.remove('selected'));

            // Add selected class to clicked option
            this.classList.add('selected');

            // Update hidden input value
            const avatarType = this.getAttribute('data-avatar');
            selectedAvatarInput.value = avatarType;

            // Add animation effect
            this.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);
        });

        // Add hover effect
        option.addEventListener('mouseenter', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'scale(1.05)';
            }
        });

        option.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = '';
            }
        });
    });
}

// Initialize auth system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
    initializeCountrySelection();
    initializeAvatarSelection();
});
