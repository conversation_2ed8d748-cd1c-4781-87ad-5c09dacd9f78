/* Custom E-Commerce Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    --box-shadow-lg: 0 10px 40px rgba(0,0,0,0.12);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

/* Base Styles */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: 700;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-secondary);
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(37, 99, 235, 0.03) 90deg, transparent 180deg, rgba(6, 182, 212, 0.03) 270deg, transparent 360deg);
    animation: rotate 20s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-image {
    animation: float 3s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-15px) scale(1.05); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Category Cards */
.category-card {
    transition: var(--transition);
    cursor: pointer;
    border-radius: var(--border-radius);
    background: white;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-card i {
    transition: var(--transition);
}

.category-card:hover i {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* Product Cards */
.product-card {
    transition: var(--transition);
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    height: 220px;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    mix-blend-mode: overlay;
}

.product-card:hover .product-image::after {
    opacity: 0.1;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-card .card-body {
    position: relative;
    z-index: 2;
    padding: 1.5rem;
}

.product-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
    transition: var(--transition);
}

.product-card:hover .product-title {
    color: var(--primary-color);
}

.product-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Price Styling */
.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--success-color);
}

.original-price {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 1rem;
    margin-left: 0.5rem;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Rating */
.rating {
    color: #ffc107;
    font-size: 0.9rem;
}

.rating .fas {
    color: #ffc107;
}

.rating .far {
    color: #dee2e6;
}

/* Navbar Actions */
.navbar-actions {
    gap: 1rem;
}

/* Enhanced Search Section */
.search-section {
    position: relative;
}

.search-wrapper {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 2px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    width: 280px;
}

.search-wrapper:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    color: white;
    padding: 10px 15px;
    font-size: 0.9rem;
    outline: none;
    border-radius: 25px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.search-input:focus {
    color: white;
}

.search-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    margin-left: 5px;
}

.search-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.search-button i {
    font-size: 0.9rem;
}

/* Enhanced Cart Section */
.cart-section {
    position: relative;
}

.cart-button {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-decoration: none;
}

.cart-button:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    color: white;
}

.cart-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-icon-wrapper i {
    font-size: 1.2rem;
    transition: var(--transition);
}

.cart-button:hover .cart-icon-wrapper i {
    transform: scale(1.1);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
    animation: pulse 2s infinite;
}

.cart-count.updated {
    animation: bounce 0.6s ease-in-out;
}

/* Additional cart button animations */
.cart-button.btn-animate {
    animation: cartShake 0.6s ease-in-out;
}

@keyframes cartShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

.cart-text {
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-10px) scale(1.2); }
    80% { transform: translateY(-5px) scale(1.1); }
}

/* Cart */
.cart-item {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
    transition: var(--transition);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0 -1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.quantity-controls button:hover {
    transform: scale(1.1);
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(13,110,253,0.3);
}

.btn-outline-primary {
    border-width: 2px;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(13,110,253,0.2);
}

/* Filter Buttons */
.filter-buttons {
    margin-bottom: 2rem;
}

.filter-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 25px;
    transition: var(--transition);
}

.filter-buttons .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(13,110,253,0.3);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #dee2e6;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.15);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.invalid-feedback {
    font-size: 0.875rem;
    color: var(--danger-color);
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 2px solid var(--light-color);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 2px solid var(--light-color);
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #343a40 100%);
}

footer h5, footer h6 {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

footer p, footer li {
    color: rgba(255,255,255,0.8);
    margin-bottom: 0.5rem;
}

footer a {
    color: rgba(255,255,255,0.8);
    transition: var(--transition);
}

footer a:hover {
    color: white;
    text-decoration: none;
}

.social-links a {
    font-size: 1.2rem;
    transition: var(--transition);
    display: inline-block;
}

.social-links a:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

/* Utilities */
.hover-shadow {
    transition: var(--transition);
}

.hover-shadow:hover {
    box-shadow: var(--box-shadow);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    z-index: 1060;
}

.toast {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 992px) {
    .search-wrapper {
        width: 240px;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .navbar-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .search-wrapper {
        width: 100%;
        max-width: 280px;
    }

    .cart-button {
        justify-content: center;
        width: 100%;
        max-width: 200px;
    }

    .hero-section {
        min-height: 300px;
        text-align: center;
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .filter-buttons {
        text-align: center;
    }

    .filter-buttons .btn {
        margin: 0.25rem;
        font-size: 0.875rem;
    }

    .category-card {
        margin-bottom: 1rem;
    }

    .product-card {
        margin-bottom: 1rem;
    }

    .modal-dialog {
        margin: 1rem;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .quantity-controls {
        justify-content: center;
        width: 100%;
    }

    /* Mobile product card adjustments */
    .product-card {
        margin-bottom: 1.5rem;
    }

    .wishlist-btn {
        width: 32px;
        height: 32px;
        top: 0.75rem;
        right: 0.75rem;
    }

    .wishlist-btn i {
        font-size: 0.8rem;
    }

    .product-buttons .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

@media (max-width: 576px) {
    .search-wrapper {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .search-input {
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    .cart-text {
        display: none;
    }

    .cart-button {
        padding: 8px 12px;
        min-width: 50px;
        justify-content: center;
    }

    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }

    .hero-section h1 {
        font-size: 1.75rem;
    }

    .hero-section .lead {
        font-size: 1rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .modal,
    .btn,
    footer {
        display: none !important;
    }
    
    .container {
        max-width: none;
        width: 100%;
    }
    
    .product-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #000000;
        --success-color: #008000;
        --danger-color: #ff0000;
        --warning-color: #ffff00;
        --info-color: #00ffff;
        --light-color: #ffffff;
        --dark-color: #000000;
    }
    
    .btn-outline-primary {
        border-width: 3px;
    }
    
    .form-control {
        border-width: 3px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-image {
        animation: none;
    }
}

/* Enhanced Wishlist Button */
.wishlist-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.95);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: var(--transition);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 2;
}

.product-card:hover .wishlist-btn {
    opacity: 1;
    transform: scale(1);
}

.wishlist-btn:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.wishlist-btn.active {
    background: var(--danger-color);
    color: white;
    opacity: 1;
    transform: scale(1);
}

.wishlist-btn i {
    font-size: 0.9rem;
    transition: var(--transition);
}

/* Product Buttons Container */
.product-buttons {
    margin-top: auto;
}

.product-buttons .btn {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.product-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Enhanced Category Cards */
.category-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.category-card:hover::before {
    left: 0;
    opacity: 0.05;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.category-card .category-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: var(--transition);
    position: relative;
    z-index: 2;
}

.category-card:hover .category-icon {
    transform: scale(1.2) rotate(5deg);
    color: var(--primary-dark);
}

.category-card h5 {
    position: relative;
    z-index: 2;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.category-card:hover h5 {
    color: var(--primary-color);
}

.category-card p {
    position: relative;
    z-index: 2;
    color: var(--secondary-color);
    margin-bottom: 0;
    transition: var(--transition);
}

.category-card:hover p {
    color: var(--dark-color);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .hero-section {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: white;
    }

    .category-card,
    .product-card {
        background: #374151;
        border-color: #4b5563;
        color: white;
    }

    .product-image {
        background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
    }

    .category-card h5,
    .product-title {
        color: white;
    }

    .category-card p,
    .product-description {
        color: #d1d5db;
    }
}

/* Hero Section Enhancements */
.hero-badge .badge {
    border-radius: 25px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.hero-stats .stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.9);
}

.hero-actions .btn {
    min-width: 150px;
}

.hero-circle-1,
.hero-circle-2 {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.1;
    animation: pulse 4s ease-in-out infinite;
}

.hero-circle-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.hero-circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    right: 20%;
    animation-delay: 2s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.1; }
    50% { transform: scale(1.1); opacity: 0.2; }
}

.floating-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: var(--primary-color);
    font-size: 1.2rem;
    animation: floatIcon 3s ease-in-out infinite;
}

.floating-icon-1 {
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.floating-icon-2 {
    top: 60%;
    right: 5%;
    animation-delay: 1s;
}

.floating-icon-3 {
    bottom: 30%;
    left: 10%;
    animation-delay: 2s;
}

.floating-icon-4 {
    top: 30%;
    left: 5%;
    animation-delay: 3s;
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

/* Promotions Banner */
.promotions-banner {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.promotions-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.countdown-timer {
    font-family: 'Courier New', monospace;
}

/* Enhanced Filter Section */
.filters-section {
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.price-filter .card {
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.view-toggle .btn {
    border-radius: var(--border-radius-sm);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-toggle .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Products Container */
.products-container {
    min-height: 400px;
    position: relative;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Category Count Badge */
.category-count {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 3;
}

.category-count .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
}

/* Floating Contact Button */
.floating-contact {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.floating-contact-btn {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.4);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.floating-contact-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 99, 235, 0.6);
}

.floating-contact-btn.active {
    transform: rotate(45deg);
    background: var(--danger-color);
}

.floating-contact-menu {
    position: absolute;
    bottom: 80px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
}

.floating-contact-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: white;
    color: var(--dark-color);
    text-decoration: none;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: var(--transition);
    white-space: nowrap;
    transform: translateX(100px);
    opacity: 0;
}

.floating-contact-menu.show .contact-option {
    transform: translateX(0);
    opacity: 1;
}

.floating-contact-menu.show .contact-option:nth-child(1) {
    transition-delay: 0.1s;
}

.floating-contact-menu.show .contact-option:nth-child(2) {
    transition-delay: 0.2s;
}

.floating-contact-menu.show .contact-option:nth-child(3) {
    transition-delay: 0.3s;
}

.contact-option:hover {
    transform: translateX(-5px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    text-decoration: none;
}

.contact-option i {
    width: 20px;
    text-align: center;
    font-size: 1.2rem;
}

.contact-option span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Platform-specific colors */
.contact-option.whatsapp:hover {
    background: #25D366;
    color: white;
}

.contact-option.telegram:hover {
    background: #0088cc;
    color: white;
}

.contact-option.facebook:hover {
    background: #1877f2;
    color: white;
}

.contact-option.whatsapp i {
    color: #25D366;
}

.contact-option.telegram i {
    color: #0088cc;
}

.contact-option.facebook i {
    color: #1877f2;
}

.contact-option.whatsapp:hover i,
.contact-option.telegram:hover i,
.contact-option.facebook:hover i {
    color: white;
}

/* Responsive adjustments for floating contact */
@media (max-width: 768px) {
    .floating-contact {
        bottom: 20px;
        right: 20px;
    }

    .floating-contact-btn {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .contact-option {
        padding: 10px 14px;
        font-size: 0.85rem;
    }

    .contact-option span {
        display: none;
    }

    .contact-option {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        justify-content: center;
        padding: 0;
    }
}

/* Enhanced Product Gallery */
.product-gallery {
    position: relative;
}

.main-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    background: var(--light-color);
}

.main-product-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition);
}

.image-zoom-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    cursor: pointer;
    color: white;
    font-size: 2rem;
}

.main-image-container:hover .image-zoom-overlay {
    opacity: 1;
}

.main-image-container:hover .main-product-image {
    transform: scale(1.05);
}

/* Thumbnail Gallery */
.thumbnail-gallery {
    margin-top: 1rem;
}

.thumbnail-image {
    width: 100%;
    height: 80px;
    object-fit: cover;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
}

.thumbnail-image:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.thumbnail-image.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

/* Product Video */
.product-video {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: var(--border-radius-sm);
}

.product-video-iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-sm);
}

/* Enhanced Product Details */
.product-details {
    padding: 1rem;
}

.price-section {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--success-color);
}

.stock-status .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.quantity-section {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
}

.quantity-section .btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-section input {
    text-align: center;
    font-weight: 600;
}

/* Specifications */
.spec-item {
    transition: var(--transition);
}

.spec-item:hover {
    background: var(--light-color);
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

/* Product Tabs */
.nav-tabs .nav-link {
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    transition: var(--transition);
}

.nav-tabs .nav-link.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
    background: white;
}

/* Enhanced Modal */
.modal-lg {
    max-width: 1000px;
}

.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Responsive adjustments for gallery */
@media (max-width: 768px) {
    .main-product-image {
        height: 300px;
    }

    .thumbnail-image {
        height: 60px;
    }

    .product-details {
        padding: 0.5rem;
        margin-top: 1rem;
    }

    .modal-lg {
        max-width: 95%;
    }
}

/* Performance Optimizations */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* Improved Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.product-card-skeleton {
    height: 400px;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
}

/* Enhanced Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .product-card {
        border: 2px solid #000;
    }

    .btn-primary {
        background: #000;
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    .navbar,
    .floating-contact,
    .modal,
    .btn {
        display: none !important;
    }

    .product-card {
        break-inside: avoid;
        margin-bottom: 1rem;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* Error states */
.error-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.error-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Success states */
.success-state {
    text-align: center;
    padding: 2rem 1rem;
    color: var(--success-color);
}

/* Enhanced form validation */
.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.valid-feedback {
    display: block;
    color: var(--success-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* User Authentication Styles */
.user-section {
    display: flex;
    align-items: center;
}

.user-menu .btn {
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-menu .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow-lg);
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Profile Styles */
.profile-avatar {
    margin-bottom: 1rem;
}

.profile-avatar i {
    color: var(--primary-color);
}

/* Order Tracking Styles */
.tracking-steps {
    position: relative;
    padding: 1rem 0;
}

.tracking-step {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    position: relative;
}

.tracking-step:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 40px;
    width: 2px;
    height: 30px;
    background: #dee2e6;
}

.tracking-step.completed:not(:last-child)::after {
    background: var(--success-color);
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 0.8rem;
    position: relative;
    z-index: 2;
}

.tracking-step.completed .step-icon {
    background: var(--success-color);
}

.step-content {
    flex-grow: 1;
}

.step-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

/* Order Card Styles */
.order-items {
    max-height: 200px;
    overflow-y: auto;
}

.order-summary {
    background: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
}

/* Auth Modal Styles */
.modal-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: 2rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive adjustments for auth */
@media (max-width: 768px) {
    .user-section {
        order: 3;
        margin-top: 1rem;
        width: 100%;
        justify-content: center;
    }

    .logged-out-state .btn {
        margin: 0 0.25rem;
    }

    .modal-body {
        padding: 1rem;
    }
}

/* Enhanced Categories Section Styles */
.categories-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.categories-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23000" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.category-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.1);
    cursor: pointer;
    position: relative;
    height: 320px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform-style: preserve-3d;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(37, 99, 235, 0.1) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.category-card:hover {
    transform: translateY(-15px) rotateX(5deg);
    box-shadow: 0 30px 60px rgba(0,0,0,0.2);
    border-color: var(--category-color, var(--primary-color));
}

.category-card:hover::before {
    opacity: 1;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

.category-image {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background: var(--category-gradient, linear-gradient(135deg, var(--primary-color), var(--secondary-color)));
}

.category-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="15" height="15" patternUnits="userSpaceOnUse"><path d="M 15 0 L 0 0 0 15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.4;
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.1) 100%);
    opacity: 0;
    transition: var(--transition);
}

.category-icon {
    font-size: 3.5rem;
    color: white;
    z-index: 2;
    position: relative;
    transition: var(--transition);
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.category-card:hover .category-icon {
    transform: scale(1.2) rotate(10deg);
    filter: drop-shadow(0 4px 15px rgba(255,255,255,0.3));
}

.category-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

.category-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.category-card:hover .category-title {
    color: var(--primary-color);
    transform: translateX(5px);
}

.category-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.category-count {
    background: var(--light-color);
    color: var(--category-color, var(--primary-color));
    padding: 0.4rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    font-weight: 600;
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.1);
}

.category-card:hover .category-count {
    background: var(--category-color, var(--primary-color));
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.category-arrow-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.category-arrow {
    color: var(--category-color, var(--primary-color));
    font-size: 1.1rem;
    transition: var(--transition);
}

.category-action-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    opacity: 0;
    transform: translateX(10px);
    transition: var(--transition);
    font-weight: 500;
}

.category-card:hover .category-arrow {
    transform: translateX(-5px);
    color: var(--category-color, var(--primary-dark));
}

.category-card:hover .category-action-text {
    opacity: 1;
    transform: translateX(0);
    color: var(--category-color, var(--primary-color));
}

/* Category Badge */
.category-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    z-index: 3;
}

.category-badge i {
    color: var(--category-color, var(--primary-color));
    font-size: 1.2rem;
    transition: var(--transition);
}

.category-card:hover .category-badge {
    background: var(--category-color, var(--primary-color));
    transform: scale(1.1) rotate(10deg);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.category-card:hover .category-badge i {
    color: white;
    transform: scale(1.1);
}

/* Category specific colors and effects */
.category-card[data-category="software"]:hover {
    box-shadow: 0 30px 60px rgba(102, 126, 234, 0.3);
    border-color: #667eea;
}

.category-card[data-category="measurement"]:hover {
    box-shadow: 0 30px 60px rgba(240, 147, 251, 0.3);
    border-color: #f093fb;
}

.category-card[data-category="welding"]:hover {
    box-shadow: 0 30px 60px rgba(79, 172, 254, 0.3);
    border-color: #4facfe;
}

.category-card[data-category="electronics"]:hover {
    box-shadow: 0 30px 60px rgba(67, 233, 123, 0.3);
    border-color: #43e97b;
}

.category-card[data-category="programmers"]:hover {
    box-shadow: 0 30px 60px rgba(255, 154, 158, 0.3);
    border-color: #ff9a9e;
}

.category-card[data-category="used"]:hover {
    box-shadow: 0 30px 60px rgba(250, 112, 154, 0.3);
    border-color: #fa709a;
}

/* Category loading states */
.category-card.loading {
    pointer-events: none;
    opacity: 0.7;
}

.category-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: var(--category-color, var(--primary-color));
    animation: spin 1s ease-in-out infinite;
    z-index: 10;
}

/* Responsive adjustments for categories */
@media (max-width: 768px) {
    .category-card {
        height: 240px;
    }

    .category-image {
        height: 120px;
    }

    .category-icon {
        font-size: 2.5rem;
    }

    .category-content {
        padding: 1rem;
    }

    .category-title {
        font-size: 1.1rem;
    }

    .category-description {
        font-size: 0.85rem;
    }

    .category-card:hover {
        transform: translateY(-5px);
    }
}

/* Enhanced Category Animations */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.category-card.clicked {
    animation: categoryClick 0.3s ease-out;
}

@keyframes categoryClick {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.category-card.hovered {
    z-index: 10;
    position: relative;
}

/* Category loading state */
.category-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.category-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    margin: -15px 0 0 -15px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    z-index: 20;
}

/* Smooth transitions for all category elements */
.category-card * {
    transition: var(--transition);
}

/* Enhanced category stats */
.category-stats {
    opacity: 0.8;
    transition: var(--transition);
}

.category-card:hover .category-stats {
    opacity: 1;
    transform: translateY(-2px);
}

/* Category grid animations */
.category-card {
    animation: categoryFadeIn 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }
.category-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes categoryFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Category Preview Tooltip */
.category-preview {
    position: fixed;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    padding: 1rem;
    min-width: 250px;
    max-width: 300px;
    z-index: 1000;
    opacity: 0;
    transform: translateX(-10px);
    transition: var(--transition);
    border: 1px solid rgba(0,0,0,0.1);
    display: none;
}

.category-preview.show {
    opacity: 1;
    transform: translateX(0);
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.preview-header i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.preview-header span {
    font-weight: 600;
    color: var(--dark-color);
}

.preview-count {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.preview-products {
    max-height: 150px;
    overflow-y: auto;
}

.preview-product {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.preview-product:last-child {
    border-bottom: none;
}

.product-name {
    font-size: 0.8rem;
    color: var(--dark-color);
    flex: 1;
    margin-right: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-price {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--success-color);
}

/* Enhanced category card accessibility */
.category-card {
    outline: none;
    tabindex: 0;
}

.category-card:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.category-card:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ========================================
   VIBRANT AND DYNAMIC DESIGN ENHANCEMENTS
   ======================================== */

/* Animated Background Patterns */
.hero-section {
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: backgroundFloat 20s ease-in-out infinite;
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* Floating Particles Animation */
.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    animation: floatUp 15s infinite linear;
    opacity: 0.6;
}

.particle:nth-child(odd) {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    animation-duration: 20s;
}

.particle:nth-child(3n) {
    background: linear-gradient(45deg, #48dbfb, #0abde3);
    animation-duration: 25s;
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Enhanced Navbar with Glassmorphism */
.navbar {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Fix navbar text colors */
.navbar-brand {
    color: var(--dark-color) !important;
    font-weight: 700;
}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color) !important;
}

.dropdown-toggle::after {
    color: var(--dark-color);
}

/* Search input styling */
.search-input {
    color: var(--dark-color) !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

.search-input::placeholder {
    color: var(--text-muted) !important;
}

.search-button {
    color: var(--primary-color) !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Cart button styling */
.cart-button {
    color: var(--primary-color) !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Auth buttons styling */
.btn-outline-light {
    color: var(--dark-color) !important;
    border-color: var(--dark-color) !important;
    background: transparent !important;
}

.btn-outline-light:hover {
    color: white !important;
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* Glowing Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* Pulsing Elements */
.pulse-element {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Gradient Text Effects */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(45deg); }
}

/* Enhanced Product Cards */
.product-card {
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Animated Icons */
.animated-icon {
    transition: all 0.3s ease;
}

.animated-icon:hover {
    transform: scale(1.2) rotate(10deg);
    color: var(--primary-color);
}

/* Glowing Search Bar */
.search-wrapper {
    position: relative;
}

.search-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
    border-radius: 25px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.search-wrapper:focus-within::before {
    opacity: 1;
    animation: borderGlow 2s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Floating Contact Icons Enhancement */
.floating-contact {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-icon {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.contact-icon:hover {
    transform: scale(1.2) rotate(15deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Section Dividers */
.section-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    margin: 3rem 0;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 20px var(--primary-color);
}

/* Loading Animations */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Enhanced Color Scheme */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --shadow-light: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.07);
}

/* Glassmorphism Cards */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-light);
}

/* Enhanced Hero Section */
.hero-section {
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 25%,
        rgba(240, 147, 251, 0.1) 50%,
        rgba(79, 172, 254, 0.1) 75%,
        rgba(67, 233, 123, 0.1) 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="heroPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23heroPattern)"/></svg>');
    animation: patternMove 30s linear infinite;
    z-index: 1;
}

@keyframes patternMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(20px) translateY(20px); }
}

/* Enhanced Buttons */
.btn {
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
    position: relative;
}

.btn-outline-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    z-index: -1;
}

.btn-outline-primary:hover::before {
    width: 100%;
}

.btn-outline-primary:hover {
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
}

/* Enhanced Form Controls */
.form-control {
    border-radius: 15px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    padding: 12px 20px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
    transform: scale(1.02);
}

/* Enhanced Badges */
.badge {
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-gradient);
}

.badge-success {
    background: var(--success-gradient);
}

.badge-warning {
    background: var(--warning-gradient);
}

.badge-danger {
    background: var(--danger-gradient);
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: 15px;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.alert-primary {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
}

.alert-success {
    background: rgba(79, 172, 254, 0.1);
    border-left-color: #4facfe;
    color: #4facfe;
}

.alert-warning {
    background: rgba(67, 233, 123, 0.1);
    border-left-color: #43e97b;
    color: #43e97b;
}

.alert-danger {
    background: rgba(250, 112, 154, 0.1);
    border-left-color: #fa709a;
    color: #fa709a;
}

/* Enhanced Modal */
.modal-content {
    border: none;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-heavy);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px 20px 0 0;
}

/* Enhanced Dropdown */
.dropdown-menu {
    border: none;
    border-radius: 15px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-light);
    padding: 10px;
}

.dropdown-item {
    border-radius: 10px;
    transition: all 0.3s ease;
    margin: 2px 0;
}

.dropdown-item:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateX(5px);
}

/* Enhanced Progress Bars */
.progress {
    height: 10px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .floating-particles {
        display: none; /* Disable particles on mobile for performance */
    }

    .hero-section::before,
    .hero-section::after {
        animation-duration: 40s; /* Slower animations on mobile */
    }

    .btn {
        border-radius: 20px;
        padding: 10px 20px;
    }

    .card {
        border-radius: 15px;
    }

    .modal-content {
        border-radius: 15px;
    }
}

/* Avatar Selection Styles */
.avatar-selection {
    padding: 1rem;
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.avatar-option {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    margin: 0 auto;
}

.avatar-option i {
    font-size: 1.5rem;
    color: white;
    transition: all 0.3s ease;
}

.avatar-option:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.avatar-option.selected {
    border-color: var(--success-color);
    background: linear-gradient(135deg, var(--success-color), #38f9d7);
    transform: scale(1.15);
    box-shadow: 0 8px 25px rgba(67, 233, 123, 0.4);
}

.avatar-option.selected i {
    transform: scale(1.1);
}

/* Different gradient colors for avatar options */
.avatar-option[data-avatar="user-tie"] {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.avatar-option[data-avatar="user-graduate"] {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.avatar-option[data-avatar="user-cog"] {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.avatar-option[data-avatar="user-astronaut"] {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.avatar-option[data-avatar="user-ninja"] {
    background: linear-gradient(135deg, #fa709a, #fee140);
}

.avatar-option[data-avatar="robot"] {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
}

.avatar-option[data-avatar="cat"] {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.avatar-option[data-avatar="dragon"] {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
}

.avatar-option[data-avatar="rocket"] {
    background: linear-gradient(135deg, #48dbfb, #0abde3);
}

.avatar-option[data-avatar="crown"] {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.avatar-option[data-avatar="gem"] {
    background: linear-gradient(135deg, #e056fd, #f0932b);
}

/* User Avatar Large for Profile */
.user-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 4px solid white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.user-avatar-large i {
    font-size: 3.5rem;
    color: white;
}

.user-avatar-large:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* Different gradient colors for large avatar based on type */
.profile-avatar .user-avatar-large {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/* Profile Avatar Container */
.profile-avatar {
    position: relative;
    display: inline-block;
}

.profile-avatar::after {
    content: '';
    position: absolute;
    top: -10px;
    right: -10px;
    left: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    z-index: -1;
    opacity: 0.1;
    animation: pulse 2s infinite;
}

/* Country Selection and Phone Input Styles */
.country-phone-section {
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.country-select {
    position: relative;
}

.country-select select {
    background: white;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.country-select select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    outline: none;
}

.country-select optgroup {
    font-weight: bold;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 0.5rem;
}

.country-select option {
    padding: 0.5rem;
    font-size: 0.9rem;
}

/* Phone Input Group Styles */
.phone-input-group {
    position: relative;
}

.phone-input-group .input-group-text {
    background: var(--primary-gradient);
    color: white;
    border: 2px solid var(--primary-color);
    border-right: none;
    font-weight: 600;
    min-width: 80px;
    justify-content: center;
    transition: all 0.3s ease;
}

.phone-input-group .form-control {
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-left: none;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.phone-input-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: none;
    outline: none;
}

.phone-input-group:focus-within .input-group-text {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.phone-input-group:focus-within .form-control {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

/* Phone Help Text */
.phone-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.phone-help i {
    color: var(--primary-color);
}

/* Country Flag Icons (Optional Enhancement) */
.country-flag {
    width: 20px;
    height: 15px;
    margin-right: 0.5rem;
    border-radius: 2px;
    display: inline-block;
}

/* Enhanced Form Labels */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-label i {
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* Validation States for Country and Phone */
.country-select select.is-valid,
.phone-input-group .form-control.is-valid {
    border-color: var(--success-color);
}

.country-select select.is-invalid,
.phone-input-group .form-control.is-invalid {
    border-color: var(--danger-color);
}

.phone-input-group .form-control.is-invalid:focus {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.phone-input-group .form-control.is-valid:focus {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

/* Loading State for Country Select */
.country-select.loading select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3e%3ccircle cx='12' cy='12' r='10' stroke='%236b7280' stroke-width='2'/%3e%3cpath fill='%236b7280' d='m15 12a3 3 0 11-6 0 3 3 0 016 0z'/%3e%3c/svg%3e");
    animation: spin 1s linear infinite;
}

/* Responsive Design for Country and Phone */
@media (max-width: 768px) {
    .country-select select {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
        padding-right: 2.2rem;
    }

    .phone-input-group .input-group-text {
        min-width: 70px;
        font-size: 0.85rem;
    }

    .phone-input-group .form-control {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }
}

/* Product Information Badges */
.product-info {
    background: rgba(0, 0, 0, 0.02);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.info-badge {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-color);
    background: white;
    padding: 0.4rem 0.6rem;
    border-radius: calc(var(--border-radius) / 2);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.info-badge:hover {
    background: var(--light-bg);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-badge i {
    font-size: 0.8rem;
    width: 16px;
    text-align: center;
}

.available-countries {
    padding: 0.5rem;
    background: rgba(var(--success-rgb), 0.1);
    border-radius: calc(var(--border-radius) / 2);
    border-left: 3px solid var(--success-color);
}

.available-countries small {
    font-size: 0.75rem;
    color: var(--success-color);
    font-weight: 500;
}

/* Stock Status Indicators */
.info-badge .stock-high {
    color: var(--success-color);
}

.info-badge .stock-medium {
    color: var(--warning-color);
}

.info-badge .stock-low {
    color: var(--danger-color);
}

/* Country Flag Styling */
.country-flag {
    width: 16px;
    height: 12px;
    border-radius: 2px;
    margin-left: 0.25rem;
    display: inline-block;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

/* Enhanced Product Cards */
.product-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.product-card:hover .product-info {
    background: rgba(var(--primary-rgb), 0.05);
}

.product-card:hover .info-badge {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive Product Info */
@media (max-width: 768px) {
    .product-info {
        padding: 0.5rem;
    }

    .info-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
    }

    .available-countries {
        padding: 0.4rem;
    }

    .available-countries small {
        font-size: 0.7rem;
    }
}

/* Category Cards Styles */
.category-cards-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 20px;
    margin-bottom: 3rem;
    position: relative;
}

.category-cards-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.category-card {
    position: relative;
    background: white;
    border-radius: 20px;
    padding: 2rem 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 2px solid transparent;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
    z-index: 1;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.category-card:hover::before {
    opacity: 0.1;
}

.category-icon {
    position: relative;
    z-index: 2;
    width: 70px;
    height: 70px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.category-icon::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-icon i {
    font-size: 2rem;
    color: white;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.category-info {
    position: relative;
    z-index: 2;
}

.category-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    line-height: 1.3;
    transition: all 0.3s ease;
    letter-spacing: 0.5px;
}

/* Category count styles removed */

/* Category Card Hover Effects */
.category-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-color);
}

.category-card:hover::before {
    opacity: 0.15;
}

.category-card:hover .category-icon {
    transform: scale(1.15) rotate(10deg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.category-card:hover .category-icon::before {
    opacity: 1;
}

.category-card:hover .category-icon i {
    transform: scale(1.1) rotate(-5deg);
}

.category-card:hover .category-title {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Category count hover effects removed */

/* Active Category Card */
.category-card.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-color: var(--primary-color);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 45px rgba(102, 126, 234, 0.4);
    position: relative;
}

.category-card.active::before {
    opacity: 0.2;
}

.category-card.active .category-icon {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.category-card.active .category-title {
    color: white;
    font-weight: 800;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.category-card.active:hover {
    transform: translateY(-12px) scale(1.05);
    box-shadow: 0 20px 55px rgba(102, 126, 234, 0.5);
}

/* Individual Category Colors */
.category-card[data-filter="software"] .category-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.category-card[data-filter="measurement"] .category-icon {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.category-card[data-filter="welding"] .category-icon {
    background: linear-gradient(135deg, #ff6b6b, #feca57);
}

.category-card[data-filter="electronics"] .category-icon {
    background: linear-gradient(135deg, #feca57, #ff9a9e);
}

.category-card[data-filter="programmers"] .category-icon {
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
}

.category-card[data-filter="used"] .category-icon {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

/* Category Card Animation */
@keyframes categoryPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.category-card.pulse {
    animation: categoryPulse 0.6s ease-in-out;
}

/* Responsive Category Cards */
@media (max-width: 1200px) {
    .category-cards-section {
        padding: 2.5rem 0;
    }

    .category-card {
        height: 160px;
        padding: 1.5rem 1rem;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 0.75rem;
    }

    .category-icon i {
        font-size: 1.7rem;
    }

    .category-title {
        font-size: 1rem;
    }

    /* Category count responsive styles removed */
}

@media (max-width: 768px) {
    .category-cards-section {
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

    .category-card {
        height: 150px;
        padding: 1.25rem 0.75rem;
        border-radius: 15px;
    }

    .category-icon {
        width: 55px;
        height: 55px;
        margin-bottom: 0.75rem;
    }

    .category-icon i {
        font-size: 1.5rem;
    }

    .category-title {
        font-size: 0.95rem;
        line-height: 1.2;
        margin-bottom: 0.4rem;
    }

    /* Category count responsive styles removed */
}

@media (max-width: 576px) {
    .category-cards-section {
        padding: 1.5rem 0;
    }

    .category-card {
        height: 140px;
        padding: 1rem 0.5rem;
        border-radius: 12px;
    }

    .category-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 0.6rem;
    }

    .category-icon i {
        font-size: 1.3rem;
    }

    .category-title {
        font-size: 0.85rem;
        line-height: 1.1;
        margin-bottom: 0.3rem;
    }

    /* Category count responsive styles removed */

    .category-card:hover {
        transform: translateY(-8px) scale(1.02);
    }

    .category-card.active:hover {
        transform: translateY(-10px) scale(1.03);
    }
}
