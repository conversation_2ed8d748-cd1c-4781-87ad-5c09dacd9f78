/* Custom E-Commerce Styles */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1f2937;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    --box-shadow-lg: 0 10px 40px rgba(0,0,0,0.12);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

/* Base Styles */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: 700;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8);
}

.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-secondary);
    min-height: 500px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(37, 99, 235, 0.03) 90deg, transparent 180deg, rgba(6, 182, 212, 0.03) 270deg, transparent 360deg);
    animation: rotate 20s linear infinite;
    pointer-events: none;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-image {
    animation: float 3s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-15px) scale(1.05); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* Category Cards */
.category-card {
    transition: var(--transition);
    cursor: pointer;
    border-radius: var(--border-radius);
    background: white;
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.category-card i {
    transition: var(--transition);
}

.category-card:hover i {
    transform: scale(1.1);
    color: var(--primary-color);
}

/* Product Cards */
.product-card {
    transition: var(--transition);
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.product-card:hover::before {
    opacity: 1;
}

.product-image {
    height: 220px;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3.5rem;
    color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    mix-blend-mode: overlay;
}

.product-card:hover .product-image::after {
    opacity: 0.1;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-card .card-body {
    position: relative;
    z-index: 2;
    padding: 1.5rem;
}

.product-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
    line-height: 1.4;
    transition: var(--transition);
}

.product-card:hover .product-title {
    color: var(--primary-color);
}

.product-description {
    color: var(--secondary-color);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Price Styling */
.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--success-color);
}

.original-price {
    text-decoration: line-through;
    color: var(--secondary-color);
    font-size: 1rem;
    margin-left: 0.5rem;
}

.discount-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Rating */
.rating {
    color: #ffc107;
    font-size: 0.9rem;
}

.rating .fas {
    color: #ffc107;
}

.rating .far {
    color: #dee2e6;
}

/* Navbar Actions */
.navbar-actions {
    gap: 1rem;
}

/* Enhanced Search Section */
.search-section {
    position: relative;
}

.search-wrapper {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    padding: 2px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    width: 280px;
}

.search-wrapper:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    color: white;
    padding: 10px 15px;
    font-size: 0.9rem;
    outline: none;
    border-radius: 25px;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.search-input:focus {
    color: white;
}

.search-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    margin-left: 5px;
}

.search-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.search-button i {
    font-size: 0.9rem;
}

/* Enhanced Cart Section */
.cart-section {
    position: relative;
}

.cart-button {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-decoration: none;
}

.cart-button:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    color: white;
}

.cart-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-icon-wrapper i {
    font-size: 1.2rem;
    transition: var(--transition);
}

.cart-button:hover .cart-icon-wrapper i {
    transform: scale(1.1);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
    animation: pulse 2s infinite;
}

.cart-count.updated {
    animation: bounce 0.6s ease-in-out;
}

/* Additional cart button animations */
.cart-button.btn-animate {
    animation: cartShake 0.6s ease-in-out;
}

@keyframes cartShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

.cart-text {
    font-size: 0.9rem;
    font-weight: 500;
    white-space: nowrap;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-10px) scale(1.2); }
    80% { transform: translateY(-5px) scale(1.1); }
}

/* Cart */
.cart-item {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
    transition: var(--transition);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0 -1rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-controls button {
    width: 30px;
    height: 30px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.quantity-controls button:hover {
    transform: scale(1.1);
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(13,110,253,0.3);
}

.btn-outline-primary {
    border-width: 2px;
    transition: var(--transition);
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(13,110,253,0.2);
}

/* Filter Buttons */
.filter-buttons {
    margin-bottom: 2rem;
}

.filter-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 25px;
    transition: var(--transition);
}

.filter-buttons .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(13,110,253,0.3);
}

/* Forms */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #dee2e6;
    transition: var(--transition);
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13,110,253,0.15);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.invalid-feedback {
    font-size: 0.875rem;
    color: var(--danger-color);
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

/* Modals */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 2px solid var(--light-color);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 2px solid var(--light-color);
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: var(--dark-color);
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--dark-color) 0%, #343a40 100%);
}

footer h5, footer h6 {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

footer p, footer li {
    color: rgba(255,255,255,0.8);
    margin-bottom: 0.5rem;
}

footer a {
    color: rgba(255,255,255,0.8);
    transition: var(--transition);
}

footer a:hover {
    color: white;
    text-decoration: none;
}

.social-links a {
    font-size: 1.2rem;
    transition: var(--transition);
    display: inline-block;
}

.social-links a:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

/* Utilities */
.hover-shadow {
    transition: var(--transition);
}

.hover-shadow:hover {
    box-shadow: var(--box-shadow);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    z-index: 1060;
}

.toast {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 992px) {
    .search-wrapper {
        width: 240px;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .navbar-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    .search-wrapper {
        width: 100%;
        max-width: 280px;
    }

    .cart-button {
        justify-content: center;
        width: 100%;
        max-width: 200px;
    }

    .hero-section {
        min-height: 300px;
        text-align: center;
    }

    .hero-section h1 {
        font-size: 2rem;
    }

    .filter-buttons {
        text-align: center;
    }

    .filter-buttons .btn {
        margin: 0.25rem;
        font-size: 0.875rem;
    }

    .category-card {
        margin-bottom: 1rem;
    }

    .product-card {
        margin-bottom: 1rem;
    }

    .modal-dialog {
        margin: 1rem;
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .quantity-controls {
        justify-content: center;
        width: 100%;
    }

    /* Mobile product card adjustments */
    .product-card {
        margin-bottom: 1.5rem;
    }

    .wishlist-btn {
        width: 32px;
        height: 32px;
        top: 0.75rem;
        right: 0.75rem;
    }

    .wishlist-btn i {
        font-size: 0.8rem;
    }

    .product-buttons .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

@media (max-width: 576px) {
    .search-wrapper {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .search-input {
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    .cart-text {
        display: none;
    }

    .cart-button {
        padding: 8px 12px;
        min-width: 50px;
        justify-content: center;
    }

    .navbar-nav {
        text-align: center;
        margin-top: 1rem;
    }

    .hero-section h1 {
        font-size: 1.75rem;
    }

    .hero-section .lead {
        font-size: 1rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .modal,
    .btn,
    footer {
        display: none !important;
    }
    
    .container {
        max-width: none;
        width: 100%;
    }
    
    .product-card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --secondary-color: #000000;
        --success-color: #008000;
        --danger-color: #ff0000;
        --warning-color: #ffff00;
        --info-color: #00ffff;
        --light-color: #ffffff;
        --dark-color: #000000;
    }
    
    .btn-outline-primary {
        border-width: 3px;
    }
    
    .form-control {
        border-width: 3px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-image {
        animation: none;
    }
}

/* Enhanced Wishlist Button */
.wishlist-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.95);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0.8);
    transition: var(--transition);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 2;
}

.product-card:hover .wishlist-btn {
    opacity: 1;
    transform: scale(1);
}

.wishlist-btn:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.wishlist-btn.active {
    background: var(--danger-color);
    color: white;
    opacity: 1;
    transform: scale(1);
}

.wishlist-btn i {
    font-size: 0.9rem;
    transition: var(--transition);
}

/* Product Buttons Container */
.product-buttons {
    margin-top: auto;
}

.product-buttons .btn {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.product-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Enhanced Category Cards */
.category-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 2rem 1.5rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.category-card:hover::before {
    left: 0;
    opacity: 0.05;
}

.category-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
    border-color: var(--primary-color);
}

.category-card .category-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: var(--transition);
    position: relative;
    z-index: 2;
}

.category-card:hover .category-icon {
    transform: scale(1.2) rotate(5deg);
    color: var(--primary-dark);
}

.category-card h5 {
    position: relative;
    z-index: 2;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.category-card:hover h5 {
    color: var(--primary-color);
}

.category-card p {
    position: relative;
    z-index: 2;
    color: var(--secondary-color);
    margin-bottom: 0;
    transition: var(--transition);
}

.category-card:hover p {
    color: var(--dark-color);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .hero-section {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: white;
    }

    .category-card,
    .product-card {
        background: #374151;
        border-color: #4b5563;
        color: white;
    }

    .product-image {
        background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
    }

    .category-card h5,
    .product-title {
        color: white;
    }

    .category-card p,
    .product-description {
        color: #d1d5db;
    }
}

/* Hero Section Enhancements */
.hero-badge .badge {
    border-radius: 25px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.hero-stats .stat-item {
    padding: 1rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.hero-stats .stat-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.9);
}

.hero-actions .btn {
    min-width: 150px;
}

.hero-circle-1,
.hero-circle-2 {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.1;
    animation: pulse 4s ease-in-out infinite;
}

.hero-circle-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.hero-circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    right: 20%;
    animation-delay: 2s;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.1; }
    50% { transform: scale(1.1); opacity: 0.2; }
}

.floating-icon {
    position: absolute;
    width: 50px;
    height: 50px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    color: var(--primary-color);
    font-size: 1.2rem;
    animation: floatIcon 3s ease-in-out infinite;
}

.floating-icon-1 {
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.floating-icon-2 {
    top: 60%;
    right: 5%;
    animation-delay: 1s;
}

.floating-icon-3 {
    bottom: 30%;
    left: 10%;
    animation-delay: 2s;
}

.floating-icon-4 {
    top: 30%;
    left: 5%;
    animation-delay: 3s;
}

@keyframes floatIcon {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

/* Promotions Banner */
.promotions-banner {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.promotions-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.countdown-timer {
    font-family: 'Courier New', monospace;
}

/* Enhanced Filter Section */
.filters-section {
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.price-filter .card {
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.view-toggle .btn {
    border-radius: var(--border-radius-sm);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-toggle .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Products Container */
.products-container {
    min-height: 400px;
    position: relative;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Category Count Badge */
.category-count {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 3;
}

.category-count .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
}

/* Floating Contact Button */
.floating-contact {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.floating-contact-btn {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.4);
    transition: var(--transition);
    animation: pulse 2s infinite;
}

.floating-contact-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 99, 235, 0.6);
}

.floating-contact-btn.active {
    transform: rotate(45deg);
    background: var(--danger-color);
}

.floating-contact-menu {
    position: absolute;
    bottom: 80px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
}

.floating-contact-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.contact-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: white;
    color: var(--dark-color);
    text-decoration: none;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: var(--transition);
    white-space: nowrap;
    transform: translateX(100px);
    opacity: 0;
}

.floating-contact-menu.show .contact-option {
    transform: translateX(0);
    opacity: 1;
}

.floating-contact-menu.show .contact-option:nth-child(1) {
    transition-delay: 0.1s;
}

.floating-contact-menu.show .contact-option:nth-child(2) {
    transition-delay: 0.2s;
}

.floating-contact-menu.show .contact-option:nth-child(3) {
    transition-delay: 0.3s;
}

.contact-option:hover {
    transform: translateX(-5px) scale(1.05);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    text-decoration: none;
}

.contact-option i {
    width: 20px;
    text-align: center;
    font-size: 1.2rem;
}

.contact-option span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Platform-specific colors */
.contact-option.whatsapp:hover {
    background: #25D366;
    color: white;
}

.contact-option.telegram:hover {
    background: #0088cc;
    color: white;
}

.contact-option.facebook:hover {
    background: #1877f2;
    color: white;
}

.contact-option.whatsapp i {
    color: #25D366;
}

.contact-option.telegram i {
    color: #0088cc;
}

.contact-option.facebook i {
    color: #1877f2;
}

.contact-option.whatsapp:hover i,
.contact-option.telegram:hover i,
.contact-option.facebook:hover i {
    color: white;
}

/* Responsive adjustments for floating contact */
@media (max-width: 768px) {
    .floating-contact {
        bottom: 20px;
        right: 20px;
    }

    .floating-contact-btn {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .contact-option {
        padding: 10px 14px;
        font-size: 0.85rem;
    }

    .contact-option span {
        display: none;
    }

    .contact-option {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        justify-content: center;
        padding: 0;
    }
}
