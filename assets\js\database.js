/**
 * Enhanced Database System with Encryption
 * Professional E-commerce Database Management
 */

class SecureDatabase {
    constructor() {
        this.dbName = 'ProfessionalEcommerce';
        this.version = 1;
        this.encryptionKey = this.generateEncryptionKey();
        this.init();
    }

    /**
     * Initialize database
     */
    init() {
        this.createTables();
        this.migrateOldData();
        this.loadData();
    }

    /**
     * Migrate old data from localStorage to new encrypted system
     */
    migrateOldData() {
        try {
            const oldUsers = localStorage.getItem('users');
            if (oldUsers && !localStorage.getItem('db_users')) {
                const users = JSON.parse(oldUsers);
                console.log('Migrating', users.length, 'users to new database system...');

                users.forEach(oldUser => {
                    // Convert old user format to new format
                    const userData = {
                        firstName: oldUser.firstName,
                        lastName: oldUser.lastName,
                        email: oldUser.email,
                        phone: oldUser.phone,
                        country: oldUser.country || 'SA',
                        countryName: oldUser.countryName || 'السعودية',
                        countryCode: oldUser.countryCode || '+966',
                        password: oldUser.password, // Will be re-hashed with salt
                        avatar: oldUser.avatar || 'user'
                    };

                    // Create user in new system
                    this.createUser(userData);
                });

                // Remove old data after successful migration
                localStorage.removeItem('users');
                console.log('Migration completed successfully');
            }
        } catch (e) {
            console.error('Error during data migration:', e);
        }
    }

    /**
     * Generate encryption key
     */
    generateEncryptionKey() {
        const stored = localStorage.getItem('db_encryption_key');
        if (stored) {
            return stored;
        }
        
        const key = this.generateRandomKey(32);
        localStorage.setItem('db_encryption_key', key);
        return key;
    }

    /**
     * Generate random key
     */
    generateRandomKey(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * Simple encryption function
     */
    encrypt(text) {
        if (!text) return text;
        
        let encrypted = '';
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
            encrypted += String.fromCharCode(charCode ^ keyChar);
        }
        return btoa(encrypted); // Base64 encode
    }

    /**
     * Simple decryption function
     */
    decrypt(encryptedText) {
        if (!encryptedText) return encryptedText;
        
        try {
            const encrypted = atob(encryptedText); // Base64 decode
            let decrypted = '';
            for (let i = 0; i < encrypted.length; i++) {
                const charCode = encrypted.charCodeAt(i);
                const keyChar = this.encryptionKey.charCodeAt(i % this.encryptionKey.length);
                decrypted += String.fromCharCode(charCode ^ keyChar);
            }
            return decrypted;
        } catch (e) {
            console.error('Decryption failed:', e);
            return encryptedText;
        }
    }

    /**
     * Hash password with salt
     */
    hashPassword(password, salt = null) {
        if (!salt) {
            salt = this.generateRandomKey(16);
        }
        
        // Simple hash function (in production, use bcrypt or similar)
        let hash = 0;
        const combined = password + salt + this.encryptionKey;
        
        for (let i = 0; i < combined.length; i++) {
            const char = combined.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        
        return {
            hash: Math.abs(hash).toString(36),
            salt: salt
        };
    }

    /**
     * Verify password
     */
    verifyPassword(password, storedHash, salt) {
        const computed = this.hashPassword(password, salt);
        return computed.hash === storedHash;
    }

    /**
     * Create database tables structure
     */
    createTables() {
        const tables = {
            users: {
                structure: {
                    id: 'string',
                    firstName: 'string',
                    lastName: 'string',
                    email: 'string',
                    phone: 'string',
                    country: 'string',
                    countryName: 'string',
                    countryCode: 'string',
                    passwordHash: 'string',
                    passwordSalt: 'string',
                    avatar: 'string',
                    registrationDate: 'string',
                    lastLogin: 'string',
                    isActive: 'boolean',
                    orders: 'array',
                    wishlist: 'array',
                    addresses: 'array'
                },
                encrypted_fields: ['firstName', 'lastName', 'phone'],
                indexes: ['email', 'id']
            },
            sessions: {
                structure: {
                    sessionId: 'string',
                    userId: 'string',
                    createdAt: 'string',
                    expiresAt: 'string',
                    isActive: 'boolean'
                },
                indexes: ['sessionId', 'userId']
            },
            orders: {
                structure: {
                    id: 'string',
                    userId: 'string',
                    items: 'array',
                    total: 'number',
                    status: 'string',
                    createdAt: 'string',
                    updatedAt: 'string'
                },
                encrypted_fields: ['items'],
                indexes: ['id', 'userId']
            }
        };

        localStorage.setItem('db_structure', JSON.stringify(tables));
    }

    /**
     * Load data from localStorage
     */
    loadData() {
        this.users = this.loadTable('users') || [];
        this.sessions = this.loadTable('sessions') || [];
        this.orders = this.loadTable('orders') || [];
    }

    /**
     * Load table data
     */
    loadTable(tableName) {
        try {
            const data = localStorage.getItem(`db_${tableName}`);
            if (!data) return [];

            const parsed = JSON.parse(data);
            return this.decryptTableData(tableName, parsed);
        } catch (e) {
            console.error(`Error loading table ${tableName}:`, e);
            return [];
        }
    }

    /**
     * Save table data
     */
    saveTable(tableName, data) {
        try {
            const encrypted = this.encryptTableData(tableName, data);
            localStorage.setItem(`db_${tableName}`, JSON.stringify(encrypted));
            return true;
        } catch (e) {
            console.error(`Error saving table ${tableName}:`, e);
            return false;
        }
    }

    /**
     * Encrypt table data
     */
    encryptTableData(tableName, data) {
        const structure = JSON.parse(localStorage.getItem('db_structure'));
        const tableStructure = structure[tableName];
        
        if (!tableStructure || !tableStructure.encrypted_fields) {
            return data;
        }

        return data.map(row => {
            const encryptedRow = { ...row };
            tableStructure.encrypted_fields.forEach(field => {
                if (encryptedRow[field]) {
                    encryptedRow[field] = this.encrypt(encryptedRow[field]);
                }
            });
            return encryptedRow;
        });
    }

    /**
     * Decrypt table data
     */
    decryptTableData(tableName, data) {
        const structure = JSON.parse(localStorage.getItem('db_structure'));
        const tableStructure = structure[tableName];
        
        if (!tableStructure || !tableStructure.encrypted_fields) {
            return data;
        }

        return data.map(row => {
            const decryptedRow = { ...row };
            tableStructure.encrypted_fields.forEach(field => {
                if (decryptedRow[field]) {
                    decryptedRow[field] = this.decrypt(decryptedRow[field]);
                }
            });
            return decryptedRow;
        });
    }

    /**
     * Create user
     */
    createUser(userData) {
        try {
            // Hash password
            const passwordData = this.hashPassword(userData.password);
            
            // Create user object
            const user = {
                id: this.generateUserId(),
                firstName: userData.firstName,
                lastName: userData.lastName,
                email: userData.email.toLowerCase(),
                phone: userData.phone,
                country: userData.country || 'SA',
                countryName: userData.countryName || 'السعودية',
                countryCode: userData.countryCode || '+966',
                passwordHash: passwordData.hash,
                passwordSalt: passwordData.salt,
                avatar: userData.avatar || 'user',
                registrationDate: new Date().toISOString(),
                lastLogin: new Date().toISOString(),
                isActive: true,
                orders: [],
                wishlist: [],
                addresses: []
            };

            // Add to users array
            this.users.push(user);
            
            // Save to storage
            this.saveTable('users', this.users);
            
            return { success: true, user: user };
        } catch (e) {
            console.error('Error creating user:', e);
            return { success: false, error: e.message };
        }
    }

    /**
     * Authenticate user
     */
    authenticateUser(email, password) {
        try {
            const user = this.users.find(u => u.email === email.toLowerCase());
            
            if (!user) {
                return { success: false, error: 'المستخدم غير موجود' };
            }

            if (!this.verifyPassword(password, user.passwordHash, user.passwordSalt)) {
                return { success: false, error: 'كلمة المرور غير صحيحة' };
            }

            // Update last login
            user.lastLogin = new Date().toISOString();
            this.saveTable('users', this.users);

            return { success: true, user: user };
        } catch (e) {
            console.error('Error authenticating user:', e);
            return { success: false, error: 'خطأ في تسجيل الدخول' };
        }
    }

    /**
     * Generate unique user ID
     */
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Check if email exists
     */
    emailExists(email) {
        return this.users.some(u => u.email === email.toLowerCase());
    }

    /**
     * Get user by ID
     */
    getUserById(userId) {
        return this.users.find(u => u.id === userId);
    }

    /**
     * Update user
     */
    updateUser(userId, updateData) {
        try {
            const userIndex = this.users.findIndex(u => u.id === userId);
            if (userIndex === -1) {
                return { success: false, error: 'المستخدم غير موجود' };
            }

            // Update user data
            this.users[userIndex] = { ...this.users[userIndex], ...updateData };
            
            // Save to storage
            this.saveTable('users', this.users);
            
            return { success: true, user: this.users[userIndex] };
        } catch (e) {
            console.error('Error updating user:', e);
            return { success: false, error: e.message };
        }
    }

    /**
     * Delete user
     */
    deleteUser(userId) {
        try {
            const userIndex = this.users.findIndex(u => u.id === userId);
            if (userIndex === -1) {
                return { success: false, error: 'المستخدم غير موجود' };
            }

            // Remove user
            this.users.splice(userIndex, 1);

            // Save to storage
            this.saveTable('users', this.users);

            return { success: true };
        } catch (e) {
            console.error('Error deleting user:', e);
            return { success: false, error: e.message };
        }
    }

    /**
     * Reset database (for debugging)
     */
    resetDatabase() {
        localStorage.removeItem('db_users');
        localStorage.removeItem('db_sessions');
        localStorage.removeItem('db_orders');
        localStorage.removeItem('db_structure');
        localStorage.removeItem('db_encryption_key');

        this.users = [];
        this.sessions = [];
        this.orders = [];

        console.log('Database reset completed');
    }

    /**
     * Create order
     */
    createOrder(orderData) {
        try {
            console.log('Creating order with data:', orderData);

            const order = {
                id: this.generateOrderId(),
                userId: orderData.userId,
                items: orderData.items,
                total: orderData.total,
                customerInfo: orderData.customerInfo,
                shippingAddress: orderData.shippingAddress,
                paymentMethod: orderData.paymentMethod,
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                orderNumber: this.generateOrderNumber()
            };

            console.log('Generated order:', order);

            // Add to orders array
            this.orders.push(order);
            console.log('Orders array after push:', this.orders.length);

            // Save to storage
            const saveResult = this.saveTable('orders', this.orders);
            console.log('Save table result:', saveResult);

            // Update user's orders
            const user = this.getUserById(orderData.userId);
            if (user) {
                if (!user.orders) {
                    user.orders = [];
                }
                user.orders.push(order.id);
                this.updateUser(orderData.userId, { orders: user.orders });
                console.log('Updated user orders:', user.orders);
            } else {
                console.warn('User not found for order:', orderData.userId);
            }

            return { success: true, order: order };
        } catch (e) {
            console.error('Error creating order:', e);
            return { success: false, error: e.message };
        }
    }

    /**
     * Get user orders
     */
    getUserOrders(userId) {
        try {
            return this.orders.filter(order => order.userId === userId)
                             .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        } catch (e) {
            console.error('Error getting user orders:', e);
            return [];
        }
    }

    /**
     * Get order by ID
     */
    getOrderById(orderId) {
        return this.orders.find(order => order.id === orderId);
    }

    /**
     * Update order status
     */
    updateOrderStatus(orderId, status) {
        try {
            const orderIndex = this.orders.findIndex(order => order.id === orderId);
            if (orderIndex === -1) {
                return { success: false, error: 'الطلب غير موجود' };
            }

            this.orders[orderIndex].status = status;
            this.orders[orderIndex].updatedAt = new Date().toISOString();

            this.saveTable('orders', this.orders);

            return { success: true, order: this.orders[orderIndex] };
        } catch (e) {
            console.error('Error updating order status:', e);
            return { success: false, error: e.message };
        }
    }

    /**
     * Generate unique order ID
     */
    generateOrderId() {
        return 'order_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Generate order number
     */
    generateOrderNumber() {
        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');

        return `ORD${year}${month}${day}${random}`;
    }

    /**
     * Get database statistics
     */
    getStats() {
        return {
            users: this.users.length,
            sessions: this.sessions.length,
            orders: this.orders.length,
            encryptionEnabled: !!this.encryptionKey
        };
    }
}

// Initialize global database instance
const secureDB = new SecureDatabase();

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecureDatabase;
}
