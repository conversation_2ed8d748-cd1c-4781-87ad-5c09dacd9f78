// Script to create products.json file if it doesn't exist
// This is a helper script for development

function createProductsFile() {
    const productsData = {
        "products": [
            {
                "id": "prod-001",
                "name": "لابتوب Dell XPS 13",
                "description": "لابتوب عالي الأداء مع معالج Intel Core i7 وذاكرة 16GB RAM. مثالي للعمل والدراسة.",
                "price": 1299.99,
                "originalPrice": 1499.99,
                "category": "electronics",
                "image": "",
                "rating": 4.8,
                "reviews": 245,
                "inStock": true,
                "stock": 15,
                "specifications": {
                    "المعالج": "Intel Core i7-1165G7",
                    "الذاكرة": "16GB LPDDR4x",
                    "التخزين": "512GB SSD",
                    "الشاشة": "13.4 بوصة 4K"
                },
                "tags": ["لابتوب", "Dell", "عالي الأداء", "محمول", "عمل"],
                "featured": true
            },
            {
                "id": "prod-002",
                "name": "هاتف iPhone 14 Pro",
                "description": "أحدث هاتف من Apple مع كاميرا احترافية ومعالج A16 Bionic القوي.",
                "price": 999.99,
                "originalPrice": 1199.99,
                "category": "electronics",
                "image": "",
                "rating": 4.9,
                "reviews": 892,
                "inStock": true,
                "stock": 25,
                "specifications": {
                    "الشاشة": "6.1 بوصة Super Retina XDR",
                    "المعالج": "A16 Bionic",
                    "الكاميرا": "48MP Pro camera system",
                    "التخزين": "128GB"
                },
                "tags": ["iPhone", "Apple", "هاتف ذكي", "كاميرا", "احترافي"],
                "featured": true
            },
            {
                "id": "prod-003",
                "name": "سماعات AirPods Pro",
                "description": "سماعات لاسلكية مع خاصية إلغاء الضوضاء النشط وجودة صوت استثنائية.",
                "price": 249.99,
                "originalPrice": 299.99,
                "category": "electronics",
                "image": "",
                "rating": 4.7,
                "reviews": 567,
                "inStock": true,
                "stock": 50,
                "specifications": {
                    "نوع الاتصال": "Bluetooth 5.0",
                    "البطارية": "حتى 6 ساعات",
                    "المميزات": "إلغاء الضوضاء النشط",
                    "المقاومة": "IPX4"
                },
                "tags": ["سماعات", "Apple", "لاسلكي", "إلغاء الضوضاء", "جودة عالية"],
                "featured": false
            },
            {
                "id": "prod-004",
                "name": "تيشيرت قطني مريح",
                "description": "تيشيرت من القطن الطبيعي 100% بتصميم عصري ومريح للاستخدام اليومي.",
                "price": 29.99,
                "originalPrice": 39.99,
                "category": "clothing",
                "image": "",
                "rating": 4.5,
                "reviews": 234,
                "inStock": true,
                "stock": 100,
                "specifications": {
                    "المادة": "قطن طبيعي 100%",
                    "الألوان": "أبيض، أسود، رمادي، أزرق",
                    "المقاسات": "S, M, L, XL, XXL",
                    "العناية": "غسيل آلة 30 درجة"
                },
                "tags": ["تيشيرت", "قطن", "مريح", "يومي", "عصري"],
                "featured": true
            },
            {
                "id": "prod-005",
                "name": "كتاب تطوير الذات",
                "description": "دليل شامل لتطوير الذات وتحقيق النجاح في الحياة الشخصية والمهنية.",
                "price": 24.99,
                "originalPrice": 34.99,
                "category": "books",
                "image": "",
                "rating": 4.6,
                "reviews": 189,
                "inStock": true,
                "stock": 75,
                "specifications": {
                    "الصفحات": "320 صفحة",
                    "المؤلف": "د. أحمد محمد",
                    "الناشر": "دار المعرفة",
                    "سنة النشر": "2024"
                },
                "tags": ["تطوير الذات", "نجاح", "تحفيز", "كتاب", "تنمية بشرية"],
                "featured": true
            },
            {
                "id": "prod-006",
                "name": "مصباح LED ذكي",
                "description": "مصباح LED قابل للتحكم عبر الهاتف مع إمكانية تغيير الألوان والسطوع.",
                "price": 49.99,
                "originalPrice": 69.99,
                "category": "home",
                "image": "",
                "rating": 4.4,
                "reviews": 156,
                "inStock": true,
                "stock": 40,
                "specifications": {
                    "القوة": "10 واط",
                    "الألوان": "16 مليون لون",
                    "التحكم": "تطبيق الهاتف + صوت",
                    "العمر الافتراضي": "25000 ساعة"
                },
                "tags": ["مصباح", "LED", "ذكي", "ألوان", "توفير طاقة"],
                "featured": true
            }
        ],
        "categories": [
            {
                "id": "electronics",
                "name": "الإلكترونيات",
                "description": "أحدث الأجهزة والتقنيات المتطورة",
                "icon": "laptop",
                "productCount": 3
            },
            {
                "id": "clothing",
                "name": "الملابس والإكسسوارات",
                "description": "أزياء عصرية للجميع",
                "icon": "tshirt",
                "productCount": 1
            },
            {
                "id": "books",
                "name": "الكتب والمراجع",
                "description": "المعرفة والثقافة والترفيه",
                "icon": "book",
                "productCount": 1
            },
            {
                "id": "home",
                "name": "المنزل والحديقة",
                "description": "كل ما تحتاجه لمنزل جميل ومريح",
                "icon": "home",
                "productCount": 1
            }
        ],
        "featuredProducts": [
            "prod-001", "prod-002", "prod-004", "prod-005", "prod-006"
        ],
        "meta": {
            "totalProducts": 6,
            "lastUpdated": "2025-07-03T01:00:00Z",
            "currency": "USD",
            "version": "1.0",
            "language": "ar",
            "region": "MENA"
        }
    };

    // Convert to JSON string
    const jsonString = JSON.stringify(productsData, null, 2);
    
    // Create and download the file
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'products.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('Products file created and downloaded!');
}

// Auto-run if this script is loaded
if (typeof window !== 'undefined') {
    console.log('Products file creator loaded. Call createProductsFile() to generate the file.');
}
