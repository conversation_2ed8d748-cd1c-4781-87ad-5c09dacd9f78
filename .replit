modules = ["nodejs-20", "python-3.11"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "E-Commerce Server"

[[workflows.workflow]]
name = "E-Commerce Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npx tsx server/api.ts"
waitForPort = 5000

[[ports]]
localPort = 3000
externalPort = 3000

[[ports]]
localPort = 5000
externalPort = 80
