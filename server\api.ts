import { storage } from './storage';
import express from 'express';
import cors from 'cors';
import path from 'path';

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Serve static files
app.use(express.static(path.join(__dirname, '..')));

// API Routes

// Get all products
app.get('/api/products', async (req, res) => {
  try {
    const { limit = 50, offset = 0, category, featured, search } = req.query;
    
    let products;
    
    if (search) {
      products = await storage.searchProducts(search as string, parseInt(limit as string));
    } else if (featured === 'true') {
      products = await storage.getFeaturedProducts(parseInt(limit as string));
    } else if (category) {
      const categoryId = parseInt(category as string);
      products = await storage.getProductsByCategory(categoryId, parseInt(limit as string));
    } else {
      products = await storage.getProducts(parseInt(limit as string), parseInt(offset as string));
    }
    
    res.json({
      products,
      total: products.length
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get single product
app.get('/api/products/:id', async (req, res) => {
  try {
    const productId = parseInt(req.params.id);
    const product = await storage.getProduct(productId);
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
});

// Get all categories
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await storage.getCategories();
    res.json({ categories });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get single category
app.get('/api/categories/:id', async (req, res) => {
  try {
    const categoryId = parseInt(req.params.id);
    const category = await storage.getCategory(categoryId);
    
    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }
    
    res.json(category);
  } catch (error) {
    console.error('Error fetching category:', error);
    res.status(500).json({ error: 'Failed to fetch category' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Start server
app.listen(PORT, () => {
  console.log(`API server running on port ${PORT}`);
});

export default app;