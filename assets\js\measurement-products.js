// Measurement Products Data
const measurementProducts = [
    {
        id: "ms001",
        name: "مولتيميتر رقمي احترافي Fluke 87V",
        description: "جهاز قياس متعدد الوظائف من فلوك، يقيس الجهد والتيار والمقاومة والتردد بدقة عالية. مثالي للفنيين والمهندسين.",
        price: 299.99,
        originalPrice: 450.00,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        images: [
            "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=600&h=400&fit=crop"
        ],
        rating: 4.9,
        reviews: 156,
        inStock: true,
        stock: 25,
        specifications: {
            "الماركة": "Fluke",
            "الموديل": "87V",
            "دقة الجهد": "±0.05%",
            "نطاق القياس": "1000V AC/DC",
            "الشاشة": "رقمية مع إضاءة خلفية"
        },
        tags: ["فلوك", "مولتيميتر", "قياس", "كهرباء", "احترافي"],
        featured: true
    },
    {
        id: "ms002",
        name: "أوسيلوسكوب رقمي Rigol DS1054Z",
        description: "أوسيلوسكوب رقمي 4 قنوات بتردد 50 ميجاهرتز من ريجول. مثالي لتحليل الإشارات الإلكترونية والتطوير.",
        price: 899.99,
        originalPrice: 1299.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        images: [
            "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
            "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=600&h=400&fit=crop"
        ],
        rating: 4.8,
        reviews: 89,
        inStock: true,
        stock: 12,
        specifications: {
            "الماركة": "Rigol",
            "الموديل": "DS1054Z",
            "عدد القنوات": "4",
            "التردد": "50 MHz",
            "معدل العينة": "1 GSa/s"
        },
        tags: ["ريجول", "أوسيلوسكوب", "إشارات", "تحليل", "إلكترونيات"],
        featured: true
    },
    {
        id: "ms003",
        name: "مولد إشارات Siglent SDG1032X",
        description: "مولد إشارات تعسفية بقناتين بتردد 30 ميجاهرتز. يولد موجات جيبية ومربعة ومثلثية وتعسفية بدقة عالية.",
        price: 449.99,
        originalPrice: 699.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.7,
        reviews: 67,
        inStock: true,
        stock: 18,
        specifications: {
            "الماركة": "Siglent",
            "الموديل": "SDG1032X",
            "عدد القنوات": "2",
            "التردد": "30 MHz",
            "أنواع الموجات": "جيبية، مربعة، مثلثية، تعسفية"
        },
        tags: ["سيجلنت", "مولد إشارات", "موجات", "تردد", "اختبار"],
        featured: true
    },
    {
        id: "ms004",
        name: "مقياس LCR Keysight E4980AL",
        description: "مقياس LCR عالي الدقة من كيسايت لقياس الحث والسعة والمقاومة. مثالي لاختبار المكونات الإلكترونية.",
        price: 1299.99,
        originalPrice: 1899.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.9,
        reviews: 34,
        inStock: true,
        stock: 8,
        specifications: {
            "الماركة": "Keysight",
            "الموديل": "E4980AL",
            "نطاق التردد": "20 Hz - 300 kHz",
            "دقة القياس": "0.05%",
            "المعايير": "IEEE 488.2, SCPI"
        },
        tags: ["كيسايت", "LCR", "حث", "سعة", "مقاومة"],
        featured: true
    },
    {
        id: "ms005",
        name: "مصدر طاقة قابل للبرمجة Korad KA3005D",
        description: "مصدر طاقة DC قابل للبرمجة 30V/5A مع شاشة رقمية وحماية من الحمل الزائد والقصر.",
        price: 189.99,
        originalPrice: 299.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        rating: 4.5,
        reviews: 123,
        inStock: true,
        stock: 35,
        specifications: {
            "الماركة": "Korad",
            "الموديل": "KA3005D",
            "الجهد": "0-30V",
            "التيار": "0-5A",
            "الدقة": "±0.01V, ±0.001A"
        },
        tags: ["كوراد", "مصدر طاقة", "برمجة", "حماية", "دقة"],
        featured: false
    },
    {
        id: "ms006",
        name: "محلل طيف TinySA Ultra",
        description: "محلل طيف صغير الحجم يغطي نطاق 100kHz-960MHz مع واجهة USB وبرمجيات تحليل متقدمة.",
        price: 199.99,
        originalPrice: 349.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.6,
        reviews: 78,
        inStock: true,
        stock: 22,
        specifications: {
            "الماركة": "TinySA",
            "الموديل": "Ultra",
            "نطاق التردد": "100kHz - 960MHz",
            "الحساسية": "-110 dBm",
            "الواجهة": "USB"
        },
        tags: ["TinySA", "محلل طيف", "تردد", "USB", "صغير"],
        featured: false
    },
    {
        id: "ms007",
        name: "مقياس حرارة بالأشعة تحت الحمراء Fluke 62 MAX+",
        description: "مقياس حرارة بالليزر مقاوم للغبار والماء IP54 مع نطاق قياس -30°C إلى +650°C.",
        price: 149.99,
        originalPrice: 229.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.7,
        reviews: 145,
        inStock: true,
        stock: 45,
        specifications: {
            "الماركة": "Fluke",
            "الموديل": "62 MAX+",
            "نطاق القياس": "-30°C إلى +650°C",
            "الدقة": "±1.5°C",
            "الحماية": "IP54"
        },
        tags: ["فلوك", "حرارة", "ليزر", "أشعة تحت حمراء", "مقاوم"],
        featured: false
    },
    {
        id: "ms008",
        name: "مقياس الذبذبات والاهتزاز PCE-VT 2700",
        description: "جهاز قياس الاهتزاز والذبذبات للآلات الصناعية مع ذاكرة تخزين وتحليل البيانات.",
        price: 399.99,
        originalPrice: 599.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        rating: 4.4,
        reviews: 56,
        inStock: true,
        stock: 15,
        specifications: {
            "الماركة": "PCE",
            "الموديل": "VT 2700",
            "نطاق التردد": "10 Hz - 1 kHz",
            "الحساسية": "0.1 m/s²",
            "الذاكرة": "99 قراءة"
        },
        tags: ["PCE", "اهتزاز", "ذبذبات", "صناعي", "تحليل"],
        featured: false
    },
    {
        id: "ms009",
        name: "مقياس الضوء الرقمي Extech LT300",
        description: "مقياس شدة الإضاءة الرقمي مع نطاق قياس واسع ودقة عالية لتطبيقات الإضاءة المختلفة.",
        price: 89.99,
        originalPrice: 149.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.3,
        reviews: 89,
        inStock: true,
        stock: 38,
        specifications: {
            "الماركة": "Extech",
            "الموديل": "LT300",
            "نطاق القياس": "0 - 50,000 Lux",
            "الدقة": "±3%",
            "الاستجابة": "طيفية CIE"
        },
        tags: ["Extech", "ضوء", "إضاءة", "لوكس", "رقمي"],
        featured: false
    },
    {
        id: "ms010",
        name: "مقياس الرطوبة والحرارة Testo 608-H1",
        description: "جهاز قياس الرطوبة والحرارة مع شاشة كبيرة وإنذار قابل للبرمجة لمراقبة البيئة.",
        price: 129.99,
        originalPrice: 199.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.6,
        reviews: 112,
        inStock: true,
        stock: 28,
        specifications: {
            "الماركة": "Testo",
            "الموديل": "608-H1",
            "نطاق الرطوبة": "2 - 98% RH",
            "نطاق الحرارة": "-10 إلى +70°C",
            "الدقة": "±2.5% RH, ±0.4°C"
        },
        tags: ["تيستو", "رطوبة", "حرارة", "بيئة", "إنذار"],
        featured: false
    },
    {
        id: "ms011",
        name: "مقياس المقاومة الأرضية Fluke 1625-2",
        description: "جهاز قياس مقاومة الأرض والتأريض من فلوك مع تقنيات قياس متعددة وذاكرة تخزين البيانات.",
        price: 1899.99,
        originalPrice: 2499.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.8,
        reviews: 45,
        inStock: true,
        stock: 8,
        specifications: {
            "الماركة": "Fluke",
            "الموديل": "1625-2",
            "نطاق المقاومة": "0.001 Ω إلى 299.9 kΩ",
            "طرق القياس": "3-pole, 4-pole, Stakeless",
            "الذاكرة": "1500 قراءة"
        },
        tags: ["فلوك", "مقاومة أرضية", "تأريض", "كهرباء", "أمان"],
        featured: true
    },
    {
        id: "ms012",
        name: "مقياس العزل الكهربائي Megger MIT1025",
        description: "جهاز قياس مقاومة العزل الكهربائي 10 كيلو فولت مع اختبار DAR وPI وحماية من الجهد العكسي.",
        price: 1299.99,
        originalPrice: 1799.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        rating: 4.7,
        reviews: 67,
        inStock: true,
        stock: 12,
        specifications: {
            "الماركة": "Megger",
            "الموديل": "MIT1025",
            "جهد الاختبار": "50V إلى 10kV",
            "نطاق المقاومة": "1 MΩ إلى 35 TΩ",
            "الاختبارات": "DAR, PI, Ramp"
        },
        tags: ["ميجر", "عزل كهربائي", "مقاومة", "عالي الجهد", "اختبار"],
        featured: true
    },
    {
        id: "ms013",
        name: "محلل جودة الطاقة Fluke 435-II",
        description: "محلل جودة الطاقة الكهربائية ثلاثي الطور مع قياس التوافقيات والوميض وعدم التوازن.",
        price: 4999.99,
        originalPrice: 6999.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.9,
        reviews: 23,
        inStock: true,
        stock: 5,
        specifications: {
            "الماركة": "Fluke",
            "الموديل": "435-II",
            "الأطوار": "3 أطوار + محايد",
            "التوافقيات": "حتى الرتبة 50",
            "معايير الجودة": "IEC 61000-4-30 Class A"
        },
        tags: ["فلوك", "جودة طاقة", "توافقيات", "ثلاثي طور", "تحليل"],
        featured: true
    },
    {
        id: "ms014",
        name: "مقياس سرعة الدوران Testo 470",
        description: "جهاز قياس سرعة الدوران بالليزر وبالتلامس مع ذاكرة تخزين وإمكانية نقل البيانات.",
        price: 299.99,
        originalPrice: 449.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.5,
        reviews: 78,
        inStock: true,
        stock: 20,
        specifications: {
            "الماركة": "Testo",
            "الموديل": "470",
            "نطاق القياس": "1 إلى 99,999 RPM",
            "طرق القياس": "ليزر، تلامس",
            "الذاكرة": "100 قراءة"
        },
        tags: ["تيستو", "سرعة دوران", "ليزر", "RPM", "محركات"],
        featured: false
    },
    {
        id: "ms015",
        name: "مقياس الضغط الرقمي Keller LEO 5",
        description: "مقياس ضغط رقمي عالي الدقة مع مجسات قابلة للتبديل ونطاق قياس واسع.",
        price: 899.99,
        originalPrice: 1299.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        rating: 4.6,
        reviews: 56,
        inStock: true,
        stock: 15,
        specifications: {
            "الماركة": "Keller",
            "الموديل": "LEO 5",
            "نطاق الضغط": "0 إلى 1000 bar",
            "الدقة": "0.01% FS",
            "المجسات": "قابلة للتبديل"
        },
        tags: ["كيلر", "ضغط", "رقمي", "دقة عالية", "مجسات"],
        featured: false
    },
    {
        id: "ms016",
        name: "مقياس التدفق بالموجات فوق الصوتية",
        description: "جهاز قياس التدفق بالموجات فوق الصوتية للسوائل مع مجسات خارجية وشاشة ملونة.",
        price: 1599.99,
        originalPrice: 2299.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.4,
        reviews: 34,
        inStock: true,
        stock: 10,
        specifications: {
            "التقنية": "موجات فوق صوتية",
            "نطاق التدفق": "0.01 إلى 40 m/s",
            "دقة القياس": "±1%",
            "أقطار الأنابيب": "13mm إلى 6000mm",
            "الشاشة": "ملونة 5.7 بوصة"
        },
        tags: ["تدفق", "موجات فوق صوتية", "سوائل", "أنابيب", "صناعي"],
        featured: false
    },
    {
        id: "ms017",
        name: "مقياس الصوت الرقمي PCE-322A",
        description: "مقياس مستوى الصوت الرقمي مع تسجيل البيانات وتحليل التردد ومعايرة تلقائية.",
        price: 199.99,
        originalPrice: 329.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.3,
        reviews: 89,
        inStock: true,
        stock: 25,
        specifications: {
            "الماركة": "PCE",
            "الموديل": "322A",
            "نطاق القياس": "30 إلى 130 dB",
            "دقة القياس": "±1.5 dB",
            "تحليل التردد": "A, C weighting"
        },
        tags: ["PCE", "صوت", "ديسيبل", "ضوضاء", "بيئة"],
        featured: false
    },
    {
        id: "ms018",
        name: "مقياس سمك الطلاء Elcometer 456",
        description: "جهاز قياس سمك الطلاء والطبقات الواقية على المعادن مع مجسات متعددة وذاكرة كبيرة.",
        price: 1199.99,
        originalPrice: 1699.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
        rating: 4.7,
        reviews: 67,
        inStock: true,
        stock: 18,
        specifications: {
            "الماركة": "Elcometer",
            "الموديل": "456",
            "نطاق القياس": "0 إلى 5000 μm",
            "دقة القياس": "±1 μm",
            "المجسات": "مغناطيسية، تيار دوامي"
        },
        tags: ["إلكوميتر", "سمك طلاء", "معادن", "طبقات", "جودة"],
        featured: false
    },
    {
        id: "ms019",
        name: "مقياس الصلادة المحمول Equotip 550",
        description: "جهاز قياس الصلادة المحمول بتقنية الارتداد مع تحويل تلقائي لمقاييس الصلادة المختلفة.",
        price: 2999.99,
        originalPrice: 3999.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop",
        rating: 4.8,
        reviews: 45,
        inStock: true,
        stock: 8,
        specifications: {
            "الماركة": "Equotip",
            "الموديل": "550",
            "مقاييس الصلادة": "HRC, HRB, HB, HV, HS",
            "دقة القياس": "±3 HLD",
            "الذاكرة": "100,000 قراءة"
        },
        tags: ["إكوتيب", "صلادة", "معادن", "ارتداد", "محمول"],
        featured: true
    },
    {
        id: "ms020",
        name: "مقياس الخشونة السطحية Mitutoyo SJ-210",
        description: "جهاز قياس خشونة السطح المحمول من ميتوتويو مع معايير قياس متعددة وشاشة ملونة.",
        price: 3499.99,
        originalPrice: 4799.99,
        category: "measurement",
        image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
        rating: 4.9,
        reviews: 34,
        inStock: true,
        stock: 6,
        specifications: {
            "الماركة": "Mitutoyo",
            "الموديل": "SJ-210",
            "معايير القياس": "Ra, Rz, Rq, Rt",
            "نطاق القياس": "0.005 إلى 16 μm",
            "دقة القياس": "±10%"
        },
        tags: ["ميتوتويو", "خشونة سطح", "جودة", "دقة", "معايير"],
        featured: true
    }
];

// Load products when page loads
document.addEventListener('DOMContentLoaded', function() {
    loadMeasurementProducts();
});

function loadMeasurementProducts() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    let productsHTML = '';

    measurementProducts.forEach(product => {
        productsHTML += `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="product-card h-100" data-product-id="${product.id}">
                    <div class="product-image-container">
                        <img src="${product.image}" alt="${product.name}" class="product-image">
                        ${product.featured ? '<span class="featured-badge">مميز</span>' : ''}
                    </div>
                    <div class="product-info">
                        <h5 class="product-title">${product.name}</h5>
                        <div class="product-actions">
                            <button class="btn-details" onclick="showProductDetails('${product.id}')">
                                <i class="fas fa-info-circle"></i>
                                تفاصيل
                            </button>
                            <button class="btn-add-cart" onclick="addToCart('${product.id}')">
                                <i class="fas fa-cart-plus"></i>
                                إضافة للسلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    productsGrid.innerHTML = productsHTML;
}

function generateStars(rating) {
    let stars = '';
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
        stars += '<i class="fas fa-star text-warning"></i>';
    }
    
    if (hasHalfStar) {
        stars += '<i class="fas fa-star-half-alt text-warning"></i>';
    }
    
    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        stars += '<i class="far fa-star text-warning"></i>';
    }
    
    return stars;
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    filterProducts(searchTerm);
});

function filterProducts(searchTerm) {
    const filteredProducts = measurementProducts.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
    
    displayFilteredProducts(filteredProducts);
}

function displayFilteredProducts(products) {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    if (products.length === 0) {
        productsGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search display-1 text-muted mb-4"></i>
                <h3 class="text-muted mb-3">لم يتم العثور على منتجات</h3>
                <p class="text-muted">جرب البحث بكلمات مختلفة</p>
            </div>
        `;
        return;
    }

    let productsHTML = '';
    products.forEach(product => {
        productsHTML += `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="product-card h-100" data-product-id="${product.id}">
                    <div class="product-image-container">
                        <img src="${product.image}" alt="${product.name}" class="product-image">
                        ${product.featured ? '<span class="featured-badge">مميز</span>' : ''}
                    </div>
                    <div class="product-info">
                        <h5 class="product-title">${product.name}</h5>
                        <div class="product-actions">
                            <button class="btn-details" onclick="showProductDetails('${product.id}')">
                                <i class="fas fa-info-circle"></i>
                                تفاصيل
                            </button>
                            <button class="btn-add-cart" onclick="addToCart('${product.id}')">
                                <i class="fas fa-cart-plus"></i>
                                إضافة للسلة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    productsGrid.innerHTML = productsHTML;
}

// Show product details function
function showProductDetails(productId) {
    const product = measurementProducts.find(p => p.id === productId);
    if (!product) return;

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${product.name}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <img src="${product.image}" alt="${product.name}" class="img-fluid rounded">
                        </div>
                        <div class="col-md-6">
                            <p class="text-muted">${product.description}</p>
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    ${generateStars(product.rating)}
                                    <span class="ms-2 text-muted">(${product.reviews} مراجعة)</span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <span class="h4 text-primary">$${product.price}</span>
                                <span class="text-muted text-decoration-line-through ms-2">$${product.originalPrice}</span>
                            </div>
                            <div class="mb-3">
                                <span class="badge ${product.inStock ? 'bg-success' : 'bg-danger'}">
                                    ${product.inStock ? `متوفر (${product.stock})` : 'غير متوفر'}
                                </span>
                            </div>
                            <h6>المواصفات:</h6>
                            <ul class="list-unstyled">
                                ${Object.entries(product.specifications).map(([key, value]) =>
                                    `<li><strong>${key}:</strong> ${value}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="addToCart('${product.id}')" data-bs-dismiss="modal">
                        <i class="fas fa-cart-plus"></i> إضافة للسلة
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', function () {
        document.body.removeChild(modal);
    });
}
