/**
 * E-Commerce Main JavaScript File
 * Handles core functionality for the e-commerce store
 */

// Global variables
let products = [];
let filteredProducts = [];
let featuredProducts = [];
let currentCategory = 'all';
let currentSort = 'featured';
let productsPerPage = 12;
let currentPage = 1;
let isLoading = false;
let searchQuery = '';
let priceRange = { min: 0, max: Infinity };
let viewMode = 'grid'; // 'grid' or 'list'
let wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
let recentlyViewed = JSON.parse(localStorage.getItem('recentlyViewed') || '[]');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Initializing E-Commerce App...');

    // Show loading state
    showLoadingState();

    // Initialize components
    initializeEventListeners();
    loadProducts();
    updateCartUI();
    initializeCountdown();

    // Initialize Bootstrap tooltips
    initializeTooltips();

    // Initialize form validation
    initializeFormValidation();

    // Load user preferences
    loadUserPreferences();

    console.log('E-Commerce App initialized successfully');
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Search functionality
    const searchBtn = document.getElementById('searchBtn');
    const searchInput = document.getElementById('searchInput');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Real-time search suggestions
        searchInput.addEventListener('input', debounce(showSearchSuggestions, 300));
    }
    
    // Category filters
    const filterButtons = document.querySelectorAll('[data-filter]');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-filter');
            filterProducts(category);
            updateActiveFilter(this);
        });
    });
    
    // Category dropdown links
    const categoryLinks = document.querySelectorAll('[data-category]');
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const category = this.getAttribute('data-category');
            filterProducts(category);
        });
    });
    
    // Sort functionality
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            currentSort = this.value;
            sortProducts();
            displayProducts();
        });
    }
    
    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', loadMoreProducts);
    }
    
    // Cart button
    const cartBtn = document.getElementById('cartBtn');
    if (cartBtn) {
        cartBtn.addEventListener('click', showCart);
    }
    
    // Contact form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            if (href && href !== '#') {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    });
    
    // Handle window resize
    window.addEventListener('resize', debounce(handleResize, 250));
    
    // Handle scroll events
    window.addEventListener('scroll', debounce(handleScroll, 100));
}

/**
 * Get embedded product data as fallback
 */
function getEmbeddedProducts() {
    return [
        {
            "id": "prod-001",
            "name": "Microsoft Office 365 Pro Plus - تفعيل دائم",
            "description": "حزمة مايكروسوفت أوفيس الاحترافية الكاملة مع تفعيل مدى الحياة. تشمل Word, Excel, PowerPoint, Outlook وجميع التطبيقات الإنتاجية.",
            "price": 49.99,
            "originalPrice": 199.99,
            "category": "electronics",
            "image": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.8,
            "reviews": 245,
            "inStock": true,
            "stock": 999,
            "specifications": {
                "النوع": "تفعيل رقمي",
                "المدة": "مدى الحياة",
                "التطبيقات": "Word, Excel, PowerPoint, Outlook, OneNote",
                "التوافق": "Windows 10/11, Mac"
            },
            "tags": ["مايكروسوفت", "أوفيس", "تفعيل", "برامج", "إنتاجية"],
            "featured": true
        },
        {
            "id": "prod-002",
            "name": "Adobe Creative Suite 2024 - النسخة الكاملة",
            "description": "مجموعة أدوبي الإبداعية الاحترافية الكاملة مع تفعيل دائم. تشمل Photoshop, Illustrator, Premiere Pro, After Effects وجميع برامج التصميم والمونتاج.",
            "price": 89.99,
            "originalPrice": 599.99,
            "category": "electronics",
            "image": "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.9,
            "reviews": 892,
            "inStock": true,
            "stock": 999,
            "specifications": {
                "النوع": "تفعيل رقمي",
                "الإصدار": "2024",
                "البرامج": "Photoshop, Illustrator, Premiere Pro, After Effects",
                "التوافق": "Windows 10/11, Mac OS"
            },
            "tags": ["أدوبي", "فوتوشوب", "تصميم", "مونتاج", "إبداع"],
            "featured": true
        },
        {
            "id": "prod-003",
            "name": "جهاز القياس الرقمي Fluke 87V - احترافي",
            "description": "جهاز قياس متعدد الوظائف عالي الدقة من فلوك الأمريكية. مثالي للمهندسين والفنيين المحترفين لقياس الفولتية والتيار والمقاومة بدقة عالية.",
            "price": 349.99,
            "originalPrice": 449.99,
            "category": "clothing",
            "image": "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1562408590-e32931084e23?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.7,
            "reviews": 567,
            "inStock": true,
            "stock": 25,
            "specifications": {
                "الدقة": "±0.05%",
                "نطاق الفولتية": "1000V AC/DC",
                "نطاق التيار": "10A",
                "الشاشة": "رقمية مع إضاءة خلفية"
            },
            "tags": ["فلوك", "قياس", "فولتية", "تيار", "احترافي"],
            "featured": false
        },
        {
            "id": "prod-004",
            "name": "ماكينة لحام إنفرتر 200 أمبير - صناعية",
            "description": "ماكينة لحام إنفرتر احترافية 200 أمبير مع تقنية IGBT المتقدمة. مثالية للاستخدام الصناعي واللحام الكهربائي والأرجون TIG.",
            "price": 299.99,
            "originalPrice": 399.99,
            "category": "books",
            "image": "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.5,
            "reviews": 234,
            "inStock": true,
            "stock": 15,
            "specifications": {
                "القوة": "200 أمبير",
                "التقنية": "IGBT إنفرتر",
                "أنواع اللحام": "كهربائي، أرجون TIG",
                "الجهد": "220V/380V"
            },
            "tags": ["لحام", "إنفرتر", "كهربائي", "أرجون", "صناعي"],
            "featured": true
        },
        {
            "id": "prod-005",
            "name": "لابتوب Dell Latitude مستعمل - حالة ممتازة",
            "description": "لابتوب Dell Latitude مستعمل بحالة ممتازة ومفحوص بالكامل. تم تجديده وصيانته احترافياً. مثالي للطلاب والعمل المكتبي والاستخدام اليومي.",
            "price": 299.99,
            "originalPrice": 799.99,
            "category": "used",
            "image": "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1593642632823-8f785ba67e45?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.6,
            "reviews": 189,
            "inStock": true,
            "stock": 3,
            "specifications": {
                "المعالج": "Intel Core i5-8250U",
                "الذاكرة": "8GB DDR4",
                "التخزين": "256GB SSD",
                "الحالة": "مستعمل - ممتاز"
            },
            "tags": ["مستعمل", "لابتوب", "Dell", "وفر", "جودة"],
            "featured": true
        },
        {
            "id": "prod-006",
            "name": "برنامج Windows 11 Pro مع التفعيل",
            "description": "نسخة أصلية من ويندوز 11 برو مع مفتاح التفعيل الدائم. يشمل جميع الميزات المتقدمة والتحديثات.",
            "price": 39.99,
            "originalPrice": 199.99,
            "category": "electronics",
            "image": "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.4,
            "reviews": 156,
            "inStock": true,
            "stock": 999,
            "specifications": {
                "النوع": "نظام تشغيل",
                "الإصدار": "Windows 11 Pro",
                "التفعيل": "مدى الحياة",
                "اللغة": "متعدد اللغات"
            },
            "tags": ["ويندوز", "نظام تشغيل", "تفعيل", "مايكروسوفت", "أصلي"],
            "featured": true
        },
        {
            "id": "prod-007",
            "name": "ساعة ذكية Apple Watch",
            "description": "ساعة ذكية متطورة مع مراقبة الصحة واللياقة البدنية وإشعارات الهاتف.",
            "price": 399.99,
            "originalPrice": 499.99,
            "category": "electronics",
            "image": "",
            "rating": 4.8,
            "reviews": 678,
            "inStock": true,
            "stock": 30,
            "specifications": {
                "الشاشة": "Retina LTPO OLED",
                "المقاومة": "مقاومة للماء 50 متر",
                "البطارية": "حتى 18 ساعة",
                "المستشعرات": "نبضات القلب، أكسجين الدم"
            },
            "tags": ["ساعة ذكية", "Apple", "صحة", "لياقة", "إشعارات"],
            "featured": true
        },
        {
            "id": "prod-008",
            "name": "جينز كلاسيكي",
            "description": "بنطلون جينز كلاسيكي عالي الجودة مناسب لجميع المناسبات.",
            "price": 79.99,
            "originalPrice": 99.99,
            "category": "clothing",
            "image": "",
            "rating": 4.3,
            "reviews": 345,
            "inStock": true,
            "stock": 60,
            "specifications": {
                "المادة": "دنيم قطني مخلوط",
                "القصة": "Slim Fit",
                "الألوان": "أزرق داكن، أسود، رمادي",
                "المقاسات": "28-42"
            },
            "tags": ["جينز", "كلاسيكي", "دنيم", "مريح", "عملي"],
            "featured": false
        },
        {
            "id": "prod-009",
            "name": "كتاب الطبخ العربي",
            "description": "مجموعة رائعة من وصفات الطبخ العربي التقليدي والحديث.",
            "price": 34.99,
            "originalPrice": 44.99,
            "category": "books",
            "image": "",
            "rating": 4.7,
            "reviews": 267,
            "inStock": true,
            "stock": 45,
            "specifications": {
                "الصفحات": "280 صفحة",
                "الوصفات": "150+ وصفة",
                "الصور": "ملونة عالية الجودة",
                "الناشر": "دار الطبخ العربي"
            },
            "tags": ["طبخ", "وصفات", "عربي", "تقليدي", "مطبخ"],
            "featured": true
        },
        {
            "id": "prod-010",
            "name": "كرسي مكتب مريح",
            "description": "كرسي مكتب بتصميم مريح ودعم للظهر مع إمكانية التعديل.",
            "price": 199.99,
            "originalPrice": 249.99,
            "category": "home",
            "image": "",
            "rating": 4.5,
            "reviews": 189,
            "inStock": true,
            "stock": 25,
            "specifications": {
                "المادة": "شبك تنفس + إسفنج عالي الكثافة",
                "التعديل": "ارتفاع + إمالة + مساند أذرع",
                "الوزن المحمول": "حتى 120 كيلو",
                "الضمان": "سنتان"
            },
            "tags": ["كرسي", "مكتب", "مريح", "دعم الظهر", "قابل للتعديل"],
            "featured": true
        },
        {
            "id": "prod-020",
            "name": "أوسيلوسكوب رقمي 4 قنوات - 100MHz",
            "description": "أوسيلوسكوب رقمي احترافي عالي الدقة لقياس وتحليل الإشارات الكهربائية والذبذبات. مثالي للمهندسين والفنيين في مجال الإلكترونيات.",
            "price": 899.99,
            "originalPrice": 1299.99,
            "category": "clothing",
            "image": "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1562408590-e32931084e23?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.8,
            "reviews": 89,
            "inStock": true,
            "stock": 8,
            "specifications": {
                "عدد القنوات": "4 قنوات",
                "عرض النطاق": "100 MHz",
                "معدل العينة": "1 GSa/s",
                "الشاشة": "7 بوصة ملونة"
            },
            "tags": ["أوسيلوسكوب", "قياس", "ذبذبات", "إشارات", "رقمي"],
            "featured": true
        },
        {
            "id": "prod-021",
            "name": "قناع لحام أوتوماتيكي - تعتيم ذكي",
            "description": "قناع لحام أوتوماتيكي احترافي مع تعتيم ذكي فوري وحماية كاملة من الأشعة فوق البنفسجية والأشعة تحت الحمراء. مريح وآمن للاستخدام المطول.",
            "price": 149.99,
            "originalPrice": 199.99,
            "category": "books",
            "image": "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.6,
            "reviews": 156,
            "inStock": true,
            "stock": 20,
            "specifications": {
                "نوع التعتيم": "أوتوماتيكي",
                "مستوى الحماية": "DIN 9-13",
                "وقت التبديل": "1/25000 ثانية",
                "البطارية": "ليثيوم قابلة للشحن"
            },
            "tags": ["قناع لحام", "أوتوماتيكي", "حماية", "أمان", "مريح"],
            "featured": false
        },
        {
            "id": "prod-022",
            "name": "Samsung Galaxy S21 مستعمل - حالة ممتازة",
            "description": "هاتف Samsung Galaxy S21 مستعمل بحالة ممتازة ومفحوص بالكامل. تم تنظيفه وإعادة ضبطه. يأتي مع الشاحن الأصلي وضمان 30 يوم.",
            "price": 399.99,
            "originalPrice": 899.99,
            "category": "used",
            "image": "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1580910051074-3eb694886505?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.3,
            "reviews": 78,
            "inStock": true,
            "stock": 2,
            "specifications": {
                "الموديل": "Samsung Galaxy S21",
                "الذاكرة": "128GB",
                "الحالة": "مستعمل - جيد جداً",
                "الضمان": "30 يوم"
            },
            "tags": ["مستعمل", "سامسونج", "هاتف", "وفر", "ضمان"],
            "featured": false
        },
        {
            "id": "prod-023",
            "name": "AutoCAD 2024 Professional - النسخة الكاملة",
            "description": "برنامج أوتوكاد الاحترافي للرسم الهندسي والتصميم المعماري والميكانيكي. النسخة الكاملة مع جميع الأدوات والمكتبات وتفعيل دائم.",
            "price": 129.99,
            "originalPrice": 1999.99,
            "category": "electronics",
            "image": "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.9,
            "reviews": 234,
            "inStock": true,
            "stock": 999,
            "specifications": {
                "النوع": "برنامج رسم هندسي",
                "الإصدار": "AutoCAD 2024",
                "التفعيل": "مدى الحياة",
                "المجالات": "هندسة، معمار، تصميم"
            },
            "tags": ["أوتوكاد", "رسم هندسي", "تصميم", "معمار", "احترافي"],
            "featured": true
        },
        {
            "id": "prod-024",
            "name": "مجموعة مقاومات إلكترونية متنوعة",
            "description": "مجموعة شاملة من المقاومات الإلكترونية بقيم مختلفة. مثالية للمشاريع الإلكترونية والصيانة والتطوير.",
            "price": 15.99,
            "originalPrice": 25.99,
            "category": "home",
            "image": "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1562408590-e32931084e23?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.4,
            "reviews": 89,
            "inStock": true,
            "stock": 100,
            "specifications": {
                "العدد": "500 قطعة",
                "القيم": "1Ω إلى 10MΩ",
                "الدقة": "±5%",
                "القوة": "1/4 واط"
            },
            "tags": ["مقاومات", "إلكترونيات", "مكونات", "مشاريع", "صيانة"],
            "featured": false
        },
        {
            "id": "prod-025",
            "name": "مكثفات إلكتروليتية متنوعة",
            "description": "مجموعة مكثفات إلكتروليتية بسعات مختلفة للدوائر الإلكترونية. جودة عالية ومناسبة للاستخدام الاحترافي.",
            "price": 12.99,
            "originalPrice": 19.99,
            "category": "home",
            "image": "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1562408590-e32931084e23?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.3,
            "reviews": 67,
            "inStock": true,
            "stock": 80,
            "specifications": {
                "العدد": "120 قطعة",
                "السعة": "1µF إلى 1000µF",
                "الجهد": "16V إلى 50V",
                "النوع": "إلكتروليتي"
            },
            "tags": ["مكثفات", "إلكترونيات", "دوائر", "مكونات", "احترافي"],
            "featured": false
        },
        {
            "id": "prod-026",
            "name": "لوحة Arduino Uno R3 الأصلية",
            "description": "لوحة Arduino Uno R3 الأصلية للمشاريع الإلكترونية والبرمجة. مثالية للمبتدئين والمحترفين في عالم الإلكترونيات.",
            "price": 25.99,
            "originalPrice": 35.99,
            "category": "home",
            "image": "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=300&fit=crop",
            "images": [
                "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=600&h=400&fit=crop",
                "https://images.unsplash.com/photo-1562408590-e32931084e23?w=600&h=400&fit=crop"
            ],
            "video": "https://www.youtube.com/embed/dQw4w9WgXcQ",
            "rating": 4.7,
            "reviews": 156,
            "inStock": true,
            "stock": 30,
            "specifications": {
                "المعالج": "ATmega328P",
                "الجهد": "5V",
                "المنافذ الرقمية": "14",
                "المنافذ التناظرية": "6"
            },
            "tags": ["Arduino", "برمجة", "مشاريع", "إلكترونيات", "تعليمي"],
            "featured": true
        }
    ];
}

/**
 * Load products from JSON file or API
 */
async function loadProducts() {
    try {
        showLoadingState();

        // Try to load products from JSON file first
        try {
            // Try multiple possible paths
            const possiblePaths = ['./products.json', 'products.json', '/products.json'];
            let loadedSuccessfully = false;

            for (const path of possiblePaths) {
                try {
                    const response = await fetch(path);
                    if (response.ok) {
                        const data = await response.json();
                        if (data.products && Array.isArray(data.products)) {
                            products = data.products;
                            featuredProducts = products.filter(product => product.featured);

                            filteredProducts = [...products];

                            // Display products
                            displayProducts();
                            displayFeaturedProducts();

                            hideLoadingState();

                            console.log(`Loaded ${products.length} products (${featuredProducts.length} featured) from ${path}`);
                            loadedSuccessfully = true;
                            return;
                        }
                    }
                } catch (pathError) {
                    console.warn(`Failed to load from ${path}:`, pathError);
                }
            }

            if (!loadedSuccessfully) {
                throw new Error('Could not load products from any path');
            }

        } catch (fetchError) {
            console.warn('Failed to load from JSON file, using fallback data:', fetchError);
        }

        // Fallback: Use embedded product data
        products = getEmbeddedProducts();
        featuredProducts = products.filter(product => product.featured);

        filteredProducts = [...products];

        // Display products
        displayProducts();
        displayFeaturedProducts();

        hideLoadingState();

        console.log(`Loaded ${products.length} products (${featuredProducts.length} featured) from embedded data`);

    } catch (error) {
        console.error('Error loading products:', error);
        hideLoadingState();
        showError('فشل في تحميل المنتجات. يرجى المحاولة مرة أخرى.');
    }
}

/**
 * Fetch products from database API
 */
async function fetchProducts() {
    try {
        const response = await fetch('/api/products');
        const data = await response.json();
        return data.products || [];
    } catch (error) {
        console.error('Error fetching products:', error);
        // Fallback to JSON file if API fails
        try {
            const fallbackResponse = await fetch('/products.json');
            const fallbackData = await fallbackResponse.json();
            return fallbackData.products || [];
        } catch (fallbackError) {
            console.error('Error fetching fallback products:', fallbackError);
            return [];
        }
    }
}

/**
 * Filter products by category
 */
function filterProducts(category) {
    currentCategory = category;
    currentPage = 1;
    
    if (category === 'all') {
        filteredProducts = [...products];
    } else {
        filteredProducts = products.filter(product => {
            // Handle both category object and string
            const productCategory = product.category?.name || product.category || '';
            return productCategory.toLowerCase() === category.toLowerCase();
        });
    }
    
    sortProducts();
    displayProducts();
    updateURL();
    
    // Scroll to products section
    scrollToProducts();
}

/**
 * Sort products based on selected criteria
 */
function sortProducts() {
    switch (currentSort) {
        case 'name':
            filteredProducts.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'price-low':
            filteredProducts.sort((a, b) => a.price - b.price);
            break;
        case 'price-high':
            filteredProducts.sort((a, b) => b.price - a.price);
            break;
        case 'rating':
            filteredProducts.sort((a, b) => b.rating - a.rating);
            break;
        default:
            break;
    }
}

/**
 * Display products in the grid
 */
function displayProducts() {
    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;
    
    // Calculate products to display
    const startIndex = 0;
    const endIndex = currentPage * productsPerPage;
    const productsToShow = filteredProducts.slice(startIndex, endIndex);
    
    if (productsToShow.length === 0) {
        productsGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-search display-4 text-muted mb-3"></i>
                <h4>لا توجد منتجات</h4>
                <p class="text-muted">جرب تغيير معايير البحث أو الفلترة.</p>
                <button class="btn btn-primary mt-3" onclick="filterProducts('all')">
                    <i class="fas fa-refresh ms-2"></i>عرض جميع المنتجات
                </button>
            </div>
        `;
        return;
    }
    
    // In a real Blogger implementation, products would be rendered by the template
    // This is just for the standalone HTML version
    let productsHTML = '';
    productsToShow.forEach(product => {
        productsHTML += createProductCard(product);
    });
    
    productsGrid.innerHTML = productsHTML;
    
    // Update load more button
    updateLoadMoreButton();
    
    // Initialize product interactions
    initializeProductInteractions();
}

/**
 * Display featured products
 */
function displayFeaturedProducts() {
    const featuredGrid = document.getElementById('featuredProductsGrid');
    if (!featuredGrid) return;

    // Show only first 6 featured products
    const productsToShow = featuredProducts.slice(0, 6);

    if (productsToShow.length === 0) {
        featuredGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="fas fa-star display-4 text-muted mb-3"></i>
                <h4>لا توجد منتجات مميزة</h4>
                <p class="text-muted">سيتم إضافة المنتجات المميزة قريباً.</p>
            </div>
        `;
        return;
    }

    let productsHTML = '';
    productsToShow.forEach(product => {
        productsHTML += createProductCard(product);
    });

    featuredGrid.innerHTML = productsHTML;

    // Initialize product interactions for featured products
    initializeProductInteractions();
}

/**
 * Create product card HTML
 */
function createProductCard(product) {
    // Convert database strings to numbers for calculations
    const price = parseFloat(product.price) || 0;
    const originalPrice = parseFloat(product.originalPrice) || 0;
    const rating = parseFloat(product.rating) || 0;

    const discountPercentage = originalPrice > 0 ?
        Math.round(((originalPrice - price) / originalPrice) * 100) : 0;

    // Determine if product is in stock
    const inStock = product.inStock && product.stock > 0;

    // Get product category
    const categoryName = product.category || 'default';

    // Use product name and description
    const displayName = product.name;
    const displayDescription = product.description;

    // Check if product is in wishlist
    const isInWishlist = wishlist.includes(product.id);
    
    return `
        <div class="col-lg-4 col-md-6 mb-4 product-item" data-category="${categoryName}">
            <div class="product-card h-100">
                ${discountPercentage > 0 ? `<div class="discount-badge">-${discountPercentage}%</div>` : ''}

                <!-- Wishlist Button -->
                <button class="wishlist-btn ${isInWishlist ? 'active' : ''}"
                        onclick="toggleWishlist('${product.id}')"
                        data-product-id="${product.id}"
                        title="${isInWishlist ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}">
                    <i class="fas fa-heart"></i>
                </button>

                <div class="product-image" onclick="showProductDetail('${product.id}')">
                    ${product.image ?
                        `<img src="${product.image}" alt="${displayName}" loading="lazy">` :
                        `<i class="fas fa-${getProductIcon(categoryName)}"></i>`
                    }
                </div>

                <div class="card-body">
                    <h5 class="product-title">${displayName}</h5>
                    <p class="product-description">${displayDescription}</p>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="price-info">
                            <span class="price">$${price.toFixed(2)}</span>
                            ${originalPrice > 0 ?
                                `<span class="original-price">$${originalPrice.toFixed(2)}</span>` :
                                ''
                            }
                        </div>
                        <div class="rating">
                            ${generateStarRating(rating)}
                            <small class="text-muted ms-1">(${product.reviews || 0})</small>
                        </div>
                    </div>

                    ${!inStock ? '<div class="alert alert-warning py-2 mb-3"><small>غير متوفر حالياً</small></div>' : ''}

                    <div class="product-buttons d-flex gap-2">
                        <button class="btn btn-outline-primary flex-grow-1" onclick="showProductDetail('${product.id}')">
                            <i class="fas fa-eye ms-1"></i>التفاصيل
                        </button>
                        <button class="btn btn-primary" onclick="addToCart('${product.id}', '${displayName}', ${price})"
                                ${inStock ? '' : 'disabled'}>
                            <i class="fas fa-cart-plus ms-1"></i>
                            ${inStock ? 'أضف للسلة' : 'غير متوفر'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Get product icon based on category
 */
function getProductIcon(category) {
    const icons = {
        'electronics': 'code',        // البرامج والتفعيلات
        'clothing': 'tools',          // أجهزة الفحص والقياس
        'books': 'fire',              // أدوات اللحام
        'home': 'microchip',          // مكونات إلكترونية
        'used': 'recycle',            // مستعمل
        'sports': 'football-ball',
        'beauty': 'spa',
        'toys': 'gamepad'
    };
    if (!category) return 'shopping-bag';
    return icons[category.toLowerCase()] || 'shopping-bag';
}

/**
 * Generate star rating HTML
 */
function generateStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHTML = '';
    
    // Full stars
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }
    
    // Half star
    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }
    
    // Empty stars
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }
    
    return starsHTML;
}

/**
 * Initialize product interactions
 */
function initializeProductInteractions() {
    // Add hover effects
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * Show product detail modal with enhanced gallery and video
 */
function showProductDetail(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const modal = document.getElementById('productModal');
    const modalTitle = document.getElementById('productModalTitle');
    const modalBody = document.getElementById('productModalBody');

    modalTitle.textContent = product.name;

    // Create image gallery
    const images = product.images || [product.image];
    const mainImage = images[0] || product.image;

    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <!-- Enhanced Image Gallery -->
                <div class="product-gallery">
                    <div class="main-image-container mb-3">
                        <img src="${mainImage}" alt="${product.name}" class="main-product-image img-fluid rounded" id="mainProductImage">
                        <div class="image-zoom-overlay" onclick="openImageZoom('${mainImage}')">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>

                    <!-- Thumbnail Gallery -->
                    ${images.length > 1 ? `
                        <div class="thumbnail-gallery">
                            <div class="row g-2">
                                ${images.map((img, index) => `
                                    <div class="col-3">
                                        <img src="${img}" alt="${product.name} ${index + 1}"
                                             class="thumbnail-image img-fluid rounded ${index === 0 ? 'active' : ''}"
                                             onclick="changeMainImage('${img}', this)">
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}

                    <!-- Video Section -->
                    ${product.video ? `
                        <div class="product-video mt-3">
                            <h6 class="mb-2">
                                <i class="fas fa-play-circle ms-2"></i>فيديو المنتج
                            </h6>
                            <div class="video-container">
                                <iframe src="${product.video}"
                                        frameborder="0"
                                        allowfullscreen
                                        class="product-video-iframe">
                                </iframe>
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>

            <div class="col-md-6">
                <div class="product-details">
                    <h4 class="mb-3">${product.name}</h4>

                    <div class="rating mb-3">
                        ${generateStarRating(product.rating)}
                        <span class="ms-2 text-muted">(${product.reviews || 0} تقييم)</span>
                    </div>

                    <div class="price-section mb-4">
                        <span class="h4 text-success">$${product.price.toFixed(2)}</span>
                        ${product.originalPrice ?
                            `<span class="text-muted text-decoration-line-through ms-2">$${product.originalPrice.toFixed(2)}</span>
                             <span class="badge bg-danger ms-2">وفر ${Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%</span>` :
                            ''
                        }
                    </div>

                    <div class="product-description mb-4">
                        <p>${product.description}</p>
                    </div>

                    <!-- Stock Status -->
                    <div class="stock-status mb-3">
                        ${product.inStock ?
                            `<span class="badge bg-success">
                                <i class="fas fa-check ms-1"></i>متوفر في المخزون (${product.stock} قطعة)
                             </span>` :
                            `<span class="badge bg-danger">
                                <i class="fas fa-times ms-1"></i>غير متوفر
                             </span>`
                        }
                    </div>

                    <!-- Quantity Section -->
                    <div class="quantity-section mb-4">
                        <label class="form-label">الكمية:</label>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-secondary btn-sm" onclick="changeQuantity(-1)">-</button>
                            <input type="number" class="form-control mx-2" id="productQuantity" value="1" min="1" max="${product.stock || 10}" style="width: 80px;">
                            <button class="btn btn-outline-secondary btn-sm" onclick="changeQuantity(1)">+</button>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="product-actions mb-4">
                        <div class="d-flex gap-2 mb-3">
                            <button class="btn btn-primary flex-grow-1"
                                    onclick="addToCartFromModal('${product.id}', '${product.name}', ${product.price})"
                                    ${!product.inStock ? 'disabled' : ''}>
                                <i class="fas fa-cart-plus ms-2"></i>إضافة للسلة
                            </button>
                            <button class="btn btn-outline-danger" onclick="toggleWishlist('${product.id}')" title="إضافة للمفضلة">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>

                        <button class="btn btn-success w-100" onclick="buyNow('${product.id}')" ${!product.inStock ? 'disabled' : ''}>
                            <i class="fas fa-bolt ms-2"></i>اشتري الآن
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Specifications Tab -->
        <div class="mt-4">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="specs-tab" data-bs-toggle="tab" data-bs-target="#specs" type="button">
                        المواصفات التقنية
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button">
                        التقييمات
                    </button>
                </li>
            </ul>
            <div class="tab-content" id="productTabsContent">
                <div class="tab-pane fade show active" id="specs" role="tabpanel">
                    <div class="p-3">
                        ${Object.entries(product.specifications || {}).map(([key, value]) =>
                            `<div class="spec-item d-flex justify-content-between py-2 border-bottom">
                                <strong>${key}:</strong>
                                <span>${value}</span>
                             </div>`
                        ).join('')}
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="p-3">
                        <div class="text-center text-muted">
                            <i class="fas fa-star display-4 mb-3"></i>
                            <p>التقييمات ستكون متاحة قريباً</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Add to recently viewed
    addToRecentlyViewed(productId);
}

/**
 * Change main product image in gallery
 */
function changeMainImage(imageSrc, thumbnailElement) {
    const mainImage = document.getElementById('mainProductImage');
    if (mainImage) {
        mainImage.src = imageSrc;

        // Update active thumbnail
        document.querySelectorAll('.thumbnail-image').forEach(thumb => {
            thumb.classList.remove('active');
        });
        thumbnailElement.classList.add('active');
    }
}

/**
 * Open image zoom modal
 */
function openImageZoom(imageSrc) {
    // Create zoom modal if it doesn't exist
    let zoomModal = document.getElementById('imageZoomModal');
    if (!zoomModal) {
        zoomModal = document.createElement('div');
        zoomModal.id = 'imageZoomModal';
        zoomModal.className = 'modal fade';
        zoomModal.innerHTML = `
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">عرض الصورة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageSrc}" class="img-fluid" alt="صورة مكبرة">
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(zoomModal);
    } else {
        zoomModal.querySelector('.modal-body img').src = imageSrc;
    }

    const bsZoomModal = new bootstrap.Modal(zoomModal);
    bsZoomModal.show();
}

/**
 * Change quantity in product modal
 */
function changeQuantity(change) {
    const quantityInput = document.getElementById('productQuantity');
    if (!quantityInput) return;

    const currentValue = parseInt(quantityInput.value);
    const newValue = currentValue + change;
    const min = parseInt(quantityInput.min) || 1;
    const max = parseInt(quantityInput.max) || 10;

    if (newValue >= min && newValue <= max) {
        quantityInput.value = newValue;
    }
}

/**
 * Add to cart from product modal
 */
function addToCartFromModal(productId, productName, price) {
    const quantityInput = document.getElementById('productQuantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;
    
    for (let i = 0; i < quantity; i++) {
        addToCart(productId, productName, price);
    }
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    modal.hide();
}

/**
 * Buy now functionality
 */
function buyNow(productId) {
    const quantityInput = document.getElementById('productQuantity');
    const quantity = quantityInput ? parseInt(quantityInput.value) : 1;
    
    // Add to cart and go to checkout
    addToCartFromModal(productId, 'Product', 0);
    
    // Close modal and show checkout
    const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
    modal.hide();
    
    setTimeout(() => {
        handleCheckout();
    }, 500);
}

/**
 * Perform search
 */
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.trim().toLowerCase();
    
    if (!searchTerm) {
        filteredProducts = [...products];
    } else {
        filteredProducts = products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        );
    }
    
    currentPage = 1;
    currentCategory = 'all';
    updateActiveFilter(document.querySelector('[data-filter="all"]'));
    displayProducts();
    
    // Update URL
    updateURL({ search: searchTerm });
    
    // Show search results message
    if (searchTerm) {
        showToast(`Found ${filteredProducts.length} products for "${searchTerm}"`, 'info');
    }
}

/**
 * Show search suggestions
 */
function showSearchSuggestions() {
    const searchInput = document.getElementById('searchInput');
    const searchTerm = searchInput.value.trim().toLowerCase();
    
    if (searchTerm.length < 2) {
        hideSuggestions();
        return;
    }
    
    const suggestions = products
        .filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm)
        )
        .slice(0, 5)
        .map(product => ({
            text: product.name,
            category: product.category,
            id: product.id
        }));
    
    displaySuggestions(suggestions);
}

/**
 * Display search suggestions
 */
function displaySuggestions(suggestions) {
    let suggestionsContainer = document.getElementById('searchSuggestions');
    
    if (!suggestionsContainer) {
        suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'searchSuggestions';
        suggestionsContainer.className = 'search-suggestions position-absolute bg-white border rounded shadow-sm';
        document.querySelector('.search-container').appendChild(suggestionsContainer);
    }
    
    if (suggestions.length === 0) {
        hideSuggestions();
        return;
    }
    
    const suggestionsHTML = suggestions.map(suggestion => `
        <div class="suggestion-item p-2 border-bottom" onclick="selectSuggestion('${suggestion.text}')">
            <div class="d-flex justify-content-between">
                <span>${suggestion.text}</span>
                <small class="text-muted">${suggestion.category}</small>
            </div>
        </div>
    `).join('');
    
    suggestionsContainer.innerHTML = suggestionsHTML;
    suggestionsContainer.style.display = 'block';
}

/**
 * Hide search suggestions
 */
function hideSuggestions() {
    const suggestionsContainer = document.getElementById('searchSuggestions');
    if (suggestionsContainer) {
        suggestionsContainer.style.display = 'none';
    }
}

/**
 * Select search suggestion
 */
function selectSuggestion(suggestion) {
    document.getElementById('searchInput').value = suggestion;
    hideSuggestions();
    performSearch();
}

/**
 * Update active filter button
 */
function updateActiveFilter(activeButton) {
    const filterButtons = document.querySelectorAll('[data-filter]');
    filterButtons.forEach(button => {
        button.classList.remove('active');
    });
    
    if (activeButton) {
        activeButton.classList.add('active');
    }
}

/**
 * Load more products
 */
function loadMoreProducts() {
    currentPage++;
    displayProducts();
}

/**
 * Update load more button
 */
function updateLoadMoreButton() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;
    
    const totalProducts = filteredProducts.length;
    const displayedProducts = currentPage * productsPerPage;
    
    if (displayedProducts >= totalProducts) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.innerHTML = `
            <i class="fas fa-plus me-2"></i>
            Load More Products (${totalProducts - displayedProducts} remaining)
        `;
    }
}

/**
 * Scroll to products section
 */
function scrollToProducts() {
    const productsSection = document.getElementById('products');
    if (productsSection) {
        productsSection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

/**
 * Handle contact form submission
 */
function handleContactForm(e) {
    e.preventDefault();
    
    const form = e.target;
    
    // Validate form
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return;
    }
    
    // Get form data
    const formData = new FormData(form);
    const contactData = {
        firstName: formData.get('firstName') || document.getElementById('firstName').value,
        lastName: formData.get('lastName') || document.getElementById('lastName').value,
        email: formData.get('email') || document.getElementById('email').value,
        subject: formData.get('subject') || document.getElementById('subject').value,
        message: formData.get('message') || document.getElementById('message').value
    };
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading"></span> Sending...';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        // Reset form
        form.reset();
        form.classList.remove('was-validated');
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showToast('Message sent successfully! We\'ll get back to you soon.', 'success');
        
        console.log('Contact form submitted:', contactData);
    }, 2000);
}

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * Handle window resize
 */
function handleResize() {
    // Adjust products grid layout if needed
    const screenWidth = window.innerWidth;
    
    if (screenWidth < 576) {
        productsPerPage = 6;
    } else if (screenWidth < 768) {
        productsPerPage = 8;
    } else {
        productsPerPage = 9;
    }
    
    // Hide search suggestions on resize
    hideSuggestions();
}

/**
 * Handle scroll events
 */
function handleScroll() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    // Add/remove navbar shadow based on scroll position
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    }
    
    // Hide search suggestions on scroll
    hideSuggestions();
}

/**
 * Update URL with current state
 */
function updateURL(params = {}) {
    const url = new URL(window.location);
    
    if (currentCategory !== 'all') {
        url.searchParams.set('category', currentCategory);
    } else {
        url.searchParams.delete('category');
    }
    
    if (params.search) {
        url.searchParams.set('search', params.search);
    } else if (!params.search && currentCategory === 'all') {
        url.searchParams.delete('search');
    }
    
    if (currentSort !== 'name') {
        url.searchParams.set('sort', currentSort);
    } else {
        url.searchParams.delete('sort');
    }
    
    window.history.replaceState({}, '', url);
}

/**
 * Show error message
 */
function showError(message) {
    showToast(message, 'danger');

    // Also show error in products grid if it exists
    const productsGrid = document.getElementById('productsGrid');
    if (productsGrid) {
        productsGrid.innerHTML = `
            <div class="col-12 text-center py-5">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle display-4 text-danger mb-3"></i>
                    <h4 class="alert-heading">خطأ في التحميل</h4>
                    <p>${message}</p>
                    <hr>
                    <button class="btn btn-danger" onclick="location.reload()">
                        <i class="fas fa-redo ms-2"></i>إعادة المحاولة
                    </button>
                </div>
            </div>
        `;
    }
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Check if element is in viewport
 */
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

/**
 * Lazy load images
 */
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading if supported
if ('IntersectionObserver' in window) {
    document.addEventListener('DOMContentLoaded', lazyLoadImages);
}

/**
 * Enhanced Functions for New Features
 */

// Show loading state
function showLoadingState() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'block';
    }
}

// Hide loading state
function hideLoadingState() {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

// Initialize countdown timer
function initializeCountdown() {
    const countdownElement = document.getElementById('countdown');
    if (!countdownElement) return;

    // Set target date (30 days from now)
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 30);

    function updateCountdown() {
        const now = new Date().getTime();
        const distance = targetDate.getTime() - now;

        if (distance < 0) {
            countdownElement.innerHTML = "انتهى العرض";
            return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

        countdownElement.innerHTML = `${days} يوم ${hours} ساعة ${minutes} دقيقة`;
    }

    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
}

// Load user preferences
function loadUserPreferences() {
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode) {
        viewMode = savedViewMode;
        updateViewModeUI();
    }
}

// Save user preferences
function saveUserPreferences() {
    localStorage.setItem('viewMode', viewMode);
    localStorage.setItem('wishlist', JSON.stringify(wishlist));
    localStorage.setItem('recentlyViewed', JSON.stringify(recentlyViewed));
}

// Update view mode UI
function updateViewModeUI() {
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');

    if (gridBtn && listBtn) {
        if (viewMode === 'grid') {
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        } else {
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        }
    }
}

// Toggle view mode
function toggleViewMode(mode) {
    viewMode = mode;
    updateViewModeUI();
    displayProducts();
    saveUserPreferences();
}

// Add to wishlist
function addToWishlist(productId) {
    if (!wishlist.includes(productId)) {
        wishlist.push(productId);
        saveUserPreferences();
        showToast('تم إضافة المنتج إلى قائمة الأمنيات', 'success');
        updateWishlistUI();
    }
}

// Remove from wishlist
function removeFromWishlist(productId) {
    const index = wishlist.indexOf(productId);
    if (index > -1) {
        wishlist.splice(index, 1);
        saveUserPreferences();
        showToast('تم إزالة المنتج من قائمة الأمنيات', 'info');
        updateWishlistUI();
    }
}

// Update wishlist UI
function updateWishlistUI() {
    const wishlistButtons = document.querySelectorAll('.wishlist-btn');
    wishlistButtons.forEach(btn => {
        const productId = btn.getAttribute('data-product-id');
        if (wishlist.includes(productId)) {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fas fa-heart"></i>';
        } else {
            btn.classList.remove('active');
            btn.innerHTML = '<i class="far fa-heart"></i>';
        }
    });
}

// Add to recently viewed
function addToRecentlyViewed(productId) {
    // Remove if already exists
    const index = recentlyViewed.indexOf(productId);
    if (index > -1) {
        recentlyViewed.splice(index, 1);
    }

    // Add to beginning
    recentlyViewed.unshift(productId);

    // Keep only last 10 items
    if (recentlyViewed.length > 10) {
        recentlyViewed = recentlyViewed.slice(0, 10);
    }

    saveUserPreferences();
}

// Apply price filter
function applyPriceFilter() {
    const minPrice = parseFloat(document.getElementById('minPrice').value) || 0;
    const maxPrice = parseFloat(document.getElementById('maxPrice').value) || Infinity;

    priceRange = { min: minPrice, max: maxPrice };
    filterProducts(currentCategory);
    showToast('تم تطبيق فلتر السعر', 'success');
}

// Enhanced product filtering
function filterProducts(category) {
    currentCategory = category;
    currentPage = 1;

    // Start with all products
    filteredProducts = [...products];

    // Apply category filter
    if (category !== 'all') {
        filteredProducts = filteredProducts.filter(product =>
            product.category.toLowerCase() === category.toLowerCase()
        );
    }

    // Apply search filter
    if (searchQuery) {
        filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
            product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
        );
    }

    // Apply price filter
    filteredProducts = filteredProducts.filter(product =>
        product.price >= priceRange.min && product.price <= priceRange.max
    );

    // Sort products
    sortProducts();

    // Display results
    displayProducts();
    updateProductCount();

    // Update active filter button
    updateActiveFilter();
}

// Update product count display
function updateProductCount() {
    const currentCountElement = document.getElementById('currentCount');
    const totalCountElement = document.getElementById('totalCount');

    if (currentCountElement && totalCountElement) {
        const displayedCount = Math.min(currentPage * productsPerPage, filteredProducts.length);
        currentCountElement.textContent = displayedCount;
        totalCountElement.textContent = filteredProducts.length;
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Add to toast container
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Scroll to sections
function scrollToProducts() {
    const productsSection = document.getElementById('products');
    if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth' });
    }
}

function scrollToCategories() {
    const categoriesSection = document.getElementById('categories');
    if (categoriesSection) {
        categoriesSection.scrollIntoView({ behavior: 'smooth' });
    }
}

// Toggle wishlist
function toggleWishlist(productId) {
    if (wishlist.includes(productId)) {
        removeFromWishlist(productId);
    } else {
        addToWishlist(productId);
    }
}

// Enhanced event listeners for new features
document.addEventListener('DOMContentLoaded', function() {
    // View mode toggle buttons
    const gridViewBtn = document.getElementById('gridViewBtn');
    const listViewBtn = document.getElementById('listViewBtn');

    if (gridViewBtn) {
        gridViewBtn.addEventListener('click', () => toggleViewMode('grid'));
    }

    if (listViewBtn) {
        listViewBtn.addEventListener('click', () => toggleViewMode('list'));
    }

    // Price filter button
    const applyPriceFilterBtn = document.getElementById('applyPriceFilter');
    if (applyPriceFilterBtn) {
        applyPriceFilterBtn.addEventListener('click', applyPriceFilter);
    }

    // Category cards click handlers
    const categoryCards = document.querySelectorAll('.category-card[data-category]');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            filterProducts(category);
            scrollToProducts();
        });
    });

    // Enhanced search with real-time filtering
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            searchQuery = this.value.trim();
            filterProducts(currentCategory);
        }, 300));
    }

    // Floating contact button
    const floatingContactBtn = document.getElementById('floatingContactBtn');
    const floatingContactMenu = document.getElementById('floatingContactMenu');

    if (floatingContactBtn && floatingContactMenu) {
        floatingContactBtn.addEventListener('click', function() {
            const isActive = this.classList.contains('active');

            if (isActive) {
                this.classList.remove('active');
                floatingContactMenu.classList.remove('show');
            } else {
                this.classList.add('active');
                floatingContactMenu.classList.add('show');
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!floatingContactBtn.contains(e.target) && !floatingContactMenu.contains(e.target)) {
                floatingContactBtn.classList.remove('active');
                floatingContactMenu.classList.remove('show');
            }
        });
    }
});
if ('IntersectionObserver' in window) {
    document.addEventListener('DOMContentLoaded', lazyLoadImages);
}

/**
 * Share product function
 */
function shareProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    if (navigator.share) {
        navigator.share({
            title: product.name,
            text: product.description,
            url: window.location.href + '?product=' + productId
        }).catch(console.error);
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href + '?product=' + productId;
        navigator.clipboard.writeText(url).then(() => {
            showToast('تم نسخ رابط المنتج', 'success');
        }).catch(() => {
            showToast('لا يمكن مشاركة المنتج', 'error');
        });
    }
}

/**
 * Compare product function
 */
function compareProduct(productId) {
    // This is a placeholder for product comparison functionality
    showToast('ميزة المقارنة ستكون متاحة قريباً', 'info');
}

/**
 * Initialize performance optimizations
 */
function initializePerformanceOptimizations() {
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Preload critical resources
    const criticalResources = [
        'assets/css/custom.css',
        'assets/js/cart.js',
        'assets/js/auth.js'
    ];

    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        document.head.appendChild(link);
    });
}

/**
 * Error handling and logging
 */
function handleError(error, context = 'Unknown') {
    console.error(`Error in ${context}:`, error);

    // Show user-friendly error message
    showToast('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.', 'danger');

    // Log error for debugging (in production, send to logging service)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'exception', {
            description: `${context}: ${error.message}`,
            fatal: false
        });
    }
}

/**
 * Validate product data
 */
function validateProduct(product) {
    const requiredFields = ['id', 'name', 'price', 'category'];
    const missingFields = requiredFields.filter(field => !product[field]);

    if (missingFields.length > 0) {
        console.warn(`Product ${product.id || 'unknown'} missing fields:`, missingFields);
        return false;
    }

    return true;
}

// Export functions for global access
window.ECommerceApp = {
    filterProducts,
    performSearch,
    showProductDetail,
    addToCart,
    showCart,
    scrollToProducts,
    shareProduct,
    compareProduct
};
